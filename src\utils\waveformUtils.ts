// Utility functions for waveform processing and rendering

export interface WaveformData {
  peaks: Float32Array;
  frequencyData?: {
    low: Float32Array;
    midLow: Float32Array;
    midHigh: Float32Array;
    high: Float32Array;
  };
  duration: number;
  sampleRate: number;
}

export interface WaveformRenderOptions {
  width: number;
  height: number;
  style: 'amplitude' | 'frequency';
  colors?: {
    amplitude?: string;
    frequency?: {
      low: string;
      midLow: string;
      midHigh: string;
      high: string;
    };
  };
  playheadColor?: string;
  backgroundColor?: string;
}

// Default color schemes
export const DEFAULT_COLORS = {
  amplitude: 'rgba(59, 130, 246, 0.6)',
  frequency: {
    low: 'rgba(30, 64, 175, 0.8)',     // Dark blue for low frequencies
    midLow: 'rgba(59, 130, 246, 0.7)',  // Medium blue for mid-low
    midHigh: 'rgba(96, 165, 250, 0.6)', // Light blue for mid-high
    high: 'rgba(147, 197, 253, 0.5)',   // Very light blue for high frequencies
  },
  playhead: '#ffffff',
  background: 'transparent',
};

// Optimized waveform rendering function
export function renderWaveform(
  ctx: CanvasRenderingContext2D,
  waveformData: WaveformData,
  options: WaveformRenderOptions,
  currentTime: number = 0
): void {
  const { width, height, style } = options;
  const colors = { ...DEFAULT_COLORS, ...options.colors };
  
  // Clear canvas
  ctx.clearRect(0, 0, width, height);
  
  // Set background if specified
  if (options.backgroundColor && options.backgroundColor !== 'transparent') {
    ctx.fillStyle = options.backgroundColor;
    ctx.fillRect(0, 0, width, height);
  }

  const centerY = height / 2;
  const dataLength = waveformData.peaks.length;
  const samplesPerPixel = dataLength / width;

  if (style === 'amplitude') {
    renderAmplitudeWaveform(ctx, waveformData, width, height, centerY, samplesPerPixel, colors.amplitude);
  } else {
    renderFrequencyWaveform(ctx, waveformData, width, height, centerY, samplesPerPixel, colors.frequency);
  }

  // Draw playhead
  if (waveformData.duration > 0) {
    const progress = currentTime / waveformData.duration;
    const playheadX = progress * width;
    
    ctx.strokeStyle = options.playheadColor || colors.playhead;
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(playheadX, 0);
    ctx.lineTo(playheadX, height);
    ctx.stroke();
  }
}

function renderAmplitudeWaveform(
  ctx: CanvasRenderingContext2D,
  waveformData: WaveformData,
  width: number,
  height: number,
  centerY: number,
  samplesPerPixel: number,
  color: string
): void {
  ctx.fillStyle = color;
  
  for (let x = 0; x < width; x++) {
    const startSample = Math.floor(x * samplesPerPixel);
    const endSample = Math.floor((x + 1) * samplesPerPixel);
    
    let max = 0;
    for (let i = startSample; i < endSample && i < waveformData.peaks.length; i++) {
      max = Math.max(max, Math.abs(waveformData.peaks[i]));
    }
    
    const barHeight = max * (height - 4);
    const y = centerY - barHeight / 2;
    
    ctx.fillRect(x, y, 1, barHeight);
  }
}

function renderFrequencyWaveform(
  ctx: CanvasRenderingContext2D,
  waveformData: WaveformData,
  width: number,
  height: number,
  centerY: number,
  samplesPerPixel: number,
  colors: { low: string; midLow: string; midHigh: string; high: string }
): void {
  const { frequencyData } = waveformData;
  if (!frequencyData) return;

  const colorArray = [colors.low, colors.midLow, colors.midHigh, colors.high];
  const bands = [frequencyData.low, frequencyData.midLow, frequencyData.midHigh, frequencyData.high];

  for (let x = 0; x < width; x++) {
    const startSample = Math.floor(x * samplesPerPixel);
    const endSample = Math.floor((x + 1) * samplesPerPixel);
    
    let totalHeight = 0;
    
    // Draw each frequency band
    bands.forEach((band, bandIndex) => {
      let max = 0;
      for (let i = startSample; i < endSample && i < band.length; i++) {
        max = Math.max(max, Math.abs(band[i]));
      }
      
      const bandHeight = max * (height - 4) / bands.length;
      const y = centerY - totalHeight - bandHeight / 2;
      
      ctx.fillStyle = colorArray[bandIndex];
      ctx.fillRect(x, y, 1, bandHeight);
      
      // Also draw the mirrored part below center
      ctx.fillRect(x, centerY + totalHeight, 1, bandHeight);
      
      totalHeight += bandHeight / 2;
    });
  }
}

// Calculate seek time from canvas click position
export function calculateSeekTime(
  event: MouseEvent,
  canvas: HTMLCanvasElement,
  duration: number
): number {
  const rect = canvas.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const progress = x / rect.width;
  return Math.max(0, Math.min(duration, progress * duration));
}

// Optimize canvas for high DPI displays
export function setupHighDPICanvas(
  canvas: HTMLCanvasElement,
  width: number,
  height: number
): CanvasRenderingContext2D | null {
  const ctx = canvas.getContext('2d');
  if (!ctx) return null;

  const devicePixelRatio = window.devicePixelRatio || 1;
  
  canvas.width = width * devicePixelRatio;
  canvas.height = height * devicePixelRatio;
  canvas.style.width = `${width}px`;
  canvas.style.height = `${height}px`;
  
  ctx.scale(devicePixelRatio, devicePixelRatio);
  
  return ctx;
}

// Debounce function for resize events
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Performance monitoring utilities
export class WaveformPerformanceMonitor {
  private renderTimes: number[] = [];
  private maxSamples = 100;

  recordRenderTime(startTime: number): void {
    const renderTime = performance.now() - startTime;
    this.renderTimes.push(renderTime);
    
    if (this.renderTimes.length > this.maxSamples) {
      this.renderTimes.shift();
    }
  }

  getAverageRenderTime(): number {
    if (this.renderTimes.length === 0) return 0;
    return this.renderTimes.reduce((sum, time) => sum + time, 0) / this.renderTimes.length;
  }

  getMaxRenderTime(): number {
    return this.renderTimes.length > 0 ? Math.max(...this.renderTimes) : 0;
  }

  reset(): void {
    this.renderTimes = [];
  }
}

// Validate waveform data
export function validateWaveformData(data: any): data is WaveformData {
  return (
    data &&
    typeof data === 'object' &&
    data.peaks instanceof Float32Array &&
    typeof data.duration === 'number' &&
    typeof data.sampleRate === 'number' &&
    data.duration > 0 &&
    data.sampleRate > 0
  );
}
