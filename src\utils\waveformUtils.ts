// Utility functions for waveform processing and rendering

export interface WaveformData {
  peaks: Float32Array;
  frequencyData?: {
    low: Float32Array;
    midLow: Float32Array;
    midHigh: Float32Array;
    high: Float32Array;
  };
  duration: number;
  sampleRate: number;
}

export interface WaveformRenderOptions {
  width: number;
  height: number;
  style: 'amplitude' | 'frequency';
  colors?: {
    amplitude?: string;
    frequency?: {
      low: string;
      midLow: string;
      midHigh: string;
      high: string;
    };
  };
  playheadColor?: string;
  backgroundColor?: string;
}

// Default color schemes
export const DEFAULT_COLORS = {
  amplitude: 'rgba(96, 165, 250, 0.7)',  // Lighter blue for unplayed portion
  amplitudePlayed: 'rgba(30, 64, 175, 0.9)', // Darker blue for played portion
  frequency: {
    low: 'rgba(30, 64, 175, 0.8)',     // Dark blue for low frequencies
    midLow: 'rgba(59, 130, 246, 0.7)',  // Medium blue for mid-low
    midHigh: 'rgba(96, 165, 250, 0.6)', // Light blue for mid-high
    high: 'rgba(147, 197, 253, 0.5)',   // Very light blue for high frequencies
  },
  playhead: '#ffffff',
  background: 'transparent',
};

// Optimized waveform rendering function
export function renderWaveform(
  ctx: CanvasRenderingContext2D,
  waveformData: WaveformData,
  options: WaveformRenderOptions,
  currentTime: number = 0
): void {
  const { width, height, style } = options;
  const colors = { ...DEFAULT_COLORS, ...options.colors };

  // Clear canvas
  ctx.clearRect(0, 0, width, height);

  // Set background if specified
  if (options.backgroundColor && options.backgroundColor !== 'transparent') {
    ctx.fillStyle = options.backgroundColor;
    ctx.fillRect(0, 0, width, height);
  }

  const centerY = height / 2;
  const dataLength = waveformData.peaks.length;
  const samplesPerPixel = dataLength / width;

  if (style === 'amplitude') {
    renderAmplitudeWaveform(ctx, waveformData, width, height, centerY, samplesPerPixel, colors.amplitude, currentTime);
  } else {
    renderFrequencyWaveform(ctx, waveformData, width, height, centerY, samplesPerPixel, colors.frequency);
  }

  // Draw playhead
  if (waveformData.duration > 0) {
    const progress = currentTime / waveformData.duration;
    const playheadX = progress * width;

    ctx.strokeStyle = options.playheadColor || colors.playhead;
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(playheadX, 0);
    ctx.lineTo(playheadX, height);
    ctx.stroke();
  }
}

function renderAmplitudeWaveform(
  ctx: CanvasRenderingContext2D,
  waveformData: WaveformData,
  width: number,
  height: number,
  centerY: number,
  samplesPerPixel: number,
  color: string,
  currentTime: number = 0
): void {
  // Calculate progress position
  const progress = waveformData.duration > 0 ? currentTime / waveformData.duration : 0;
  const progressX = progress * width;

  // Improve horizontal resolution and visual clarity
  const optimalBarsCount = Math.min(width * 0.8, waveformData.peaks.length); // Don't exceed reasonable bar count
  const barWidth = Math.max(1, Math.floor(width / optimalBarsCount));
  const barSpacing = barWidth > 2 ? 1 : 0; // Add spacing for wider bars
  const effectiveBarWidth = barWidth - barSpacing;
  const effectiveSamplesPerPixel = waveformData.peaks.length / optimalBarsCount;

  for (let x = 0; x < width; x += barWidth) {
    const startSample = Math.floor(x * effectiveSamplesPerPixel);
    const endSample = Math.floor((x + barWidth) * effectiveSamplesPerPixel);

    let max = 0;
    let rms = 0;
    let sampleCount = 0;

    // Calculate both peak and RMS for better visual representation
    for (let i = startSample; i < endSample && i < waveformData.peaks.length; i++) {
      const sample = Math.abs(waveformData.peaks[i]);
      max = Math.max(max, sample);
      rms += sample * sample;
      sampleCount++;
    }

    if (sampleCount > 0) {
      rms = Math.sqrt(rms / sampleCount);
    }

    // Use a combination of peak and RMS for more natural-looking waveform
    const displayValue = Math.max(max * 0.8, rms * 1.2);
    const barHeight = Math.max(1, displayValue * (height - 8));
    const y = centerY - barHeight / 2;

    // Determine if this bar is in the played or unplayed section
    const isPlayed = x < progressX;

    // Set color based on playback progress
    if (isPlayed) {
      // Played portion - darker blue
      ctx.fillStyle = DEFAULT_COLORS.amplitudePlayed;
    } else {
      // Unplayed portion - original color
      ctx.fillStyle = color;
    }

    // Draw the waveform bar with proper spacing
    ctx.fillRect(x, y, effectiveBarWidth, barHeight);

    // The spacing is automatically handled by using effectiveBarWidth
  }
}

function renderFrequencyWaveform(
  ctx: CanvasRenderingContext2D,
  waveformData: WaveformData,
  width: number,
  height: number,
  centerY: number,
  samplesPerPixel: number,
  colors: { low: string; midLow: string; midHigh: string; high: string }
): void {
  const { frequencyData } = waveformData;
  if (!frequencyData) return;

  const colorArray = [colors.low, colors.midLow, colors.midHigh, colors.high];
  const bands = [frequencyData.low, frequencyData.midLow, frequencyData.midHigh, frequencyData.high];

  for (let x = 0; x < width; x++) {
    const startSample = Math.floor(x * samplesPerPixel);
    const endSample = Math.floor((x + 1) * samplesPerPixel);

    let totalHeight = 0;

    // Draw each frequency band
    bands.forEach((band, bandIndex) => {
      let max = 0;
      for (let i = startSample; i < endSample && i < band.length; i++) {
        max = Math.max(max, Math.abs(band[i]));
      }

      const bandHeight = max * (height - 4) / bands.length;
      const y = centerY - totalHeight - bandHeight / 2;

      ctx.fillStyle = colorArray[bandIndex];
      ctx.fillRect(x, y, 1, bandHeight);

      // Also draw the mirrored part below center
      ctx.fillRect(x, centerY + totalHeight, 1, bandHeight);

      totalHeight += bandHeight / 2;
    });
  }
}

// Calculate seek time from canvas click position
export function calculateSeekTime(
  event: MouseEvent,
  canvas: HTMLCanvasElement,
  duration: number
): number {
  const rect = canvas.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const progress = x / rect.width;
  return Math.max(0, Math.min(duration, progress * duration));
}

// Optimize canvas for high DPI displays
export function setupHighDPICanvas(
  canvas: HTMLCanvasElement,
  width: number,
  height: number
): CanvasRenderingContext2D | null {
  const ctx = canvas.getContext('2d');
  if (!ctx) return null;

  const devicePixelRatio = window.devicePixelRatio || 1;

  canvas.width = width * devicePixelRatio;
  canvas.height = height * devicePixelRatio;
  canvas.style.width = `${width}px`;
  canvas.style.height = `${height}px`;

  ctx.scale(devicePixelRatio, devicePixelRatio);

  return ctx;
}

// Debounce function for resize events
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Performance monitoring utilities
export class WaveformPerformanceMonitor {
  private renderTimes: number[] = [];
  private maxSamples = 100;

  recordRenderTime(startTime: number): void {
    const renderTime = performance.now() - startTime;
    this.renderTimes.push(renderTime);

    if (this.renderTimes.length > this.maxSamples) {
      this.renderTimes.shift();
    }
  }

  getAverageRenderTime(): number {
    if (this.renderTimes.length === 0) return 0;
    return this.renderTimes.reduce((sum, time) => sum + time, 0) / this.renderTimes.length;
  }

  getMaxRenderTime(): number {
    return this.renderTimes.length > 0 ? Math.max(...this.renderTimes) : 0;
  }

  reset(): void {
    this.renderTimes = [];
  }
}

// Validate waveform data
export function validateWaveformData(data: any): data is WaveformData {
  return (
    data &&
    typeof data === 'object' &&
    data.peaks instanceof Float32Array &&
    typeof data.duration === 'number' &&
    typeof data.sampleRate === 'number' &&
    data.duration > 0 &&
    data.sampleRate > 0
  );
}
