// Utility functions for waveform processing and rendering

export interface WaveformData {
  peaks: Float32Array;
  frequencyData?: {
    low: Float32Array;
    midLow: Float32Array;
    midHigh: Float32Array;
    high: Float32Array;
  };
  duration: number;
  sampleRate: number;
}

export interface WaveformRenderOptions {
  width: number;
  height: number;
  style: 'amplitude' | 'frequency';
  colors?: {
    amplitude?: string;
    frequency?: {
      low: string;
      midLow: string;
      midHigh: string;
      high: string;
    };
  };
  playheadColor?: string;
  backgroundColor?: string;
}

// Default color schemes
export const DEFAULT_COLORS = {
  amplitude: 'rgba(96, 165, 250, 0.7)',  // Lighter blue for unplayed portion
  amplitudePlayed: 'rgba(30, 64, 175, 0.9)', // Darker blue for played portion
  frequency: {
    low: 'rgba(30, 64, 175, 0.8)',     // Dark blue for low frequencies
    midLow: 'rgba(59, 130, 246, 0.7)',  // Medium blue for mid-low
    midHigh: 'rgba(96, 165, 250, 0.6)', // Light blue for mid-high
    high: 'rgba(147, 197, 253, 0.5)',   // Very light blue for high frequencies
  },
  playhead: '#ffffff',
  background: 'transparent',
};

// Optimized waveform rendering function
export function renderWaveform(
  ctx: CanvasRenderingContext2D,
  waveformData: WaveformData,
  options: WaveformRenderOptions,
  currentTime: number = 0
): void {
  const { width, height, style } = options;
  const colors = { ...DEFAULT_COLORS, ...options.colors };

  // Clear canvas
  ctx.clearRect(0, 0, width, height);

  // Set background if specified
  if (options.backgroundColor && options.backgroundColor !== 'transparent') {
    ctx.fillStyle = options.backgroundColor;
    ctx.fillRect(0, 0, width, height);
  }

  const centerY = height / 2;

  if (style === 'amplitude') {
    renderAmplitudeWaveform(ctx, waveformData, width, height, centerY, colors.amplitude, currentTime);
  } else {
    renderFrequencyWaveform(ctx, waveformData, width, height, centerY, currentTime);
  }

  // Draw playhead
  if (waveformData.duration > 0) {
    const progress = currentTime / waveformData.duration;
    const playheadX = progress * width;

    ctx.strokeStyle = options.playheadColor || colors.playhead;
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(playheadX, 0);
    ctx.lineTo(playheadX, height);
    ctx.stroke();
  }
}

function renderAmplitudeWaveform(
  ctx: CanvasRenderingContext2D,
  waveformData: WaveformData,
  width: number,
  height: number,
  centerY: number,
  color: string,
  currentTime: number = 0
): void {
  // Calculate progress position
  const progress = waveformData.duration > 0 ? currentTime / waveformData.duration : 0;
  const progressX = progress * width;

  // Calculate rendering parameters to ensure waveform fills entire width
  const peaksLength = waveformData.peaks.length;

  // Always render bars across the full width, scaling the data appropriately
  const barsToRender = Math.min(width, peaksLength * 2); // Allow some upsampling for clarity
  const barWidth = width / barsToRender;
  const effectiveBarWidth = Math.max(0.5, barWidth - 0.5); // Minimum bar width with spacing

  // Calculate how to map from canvas position to peaks data
  const peaksPerPixel = peaksLength / width;

  for (let i = 0; i < barsToRender; i++) {
    const x = i * barWidth;
    if (x >= width) break;

    // Map canvas position to peaks data index
    const peakIndex = (x / width) * peaksLength;
    const startSample = Math.floor(peakIndex);
    const endSample = Math.min(Math.ceil(peakIndex + peaksPerPixel), peaksLength - 1);

    let max = 0;
    let rms = 0;
    let sampleCount = 0;

    // Calculate both peak and RMS for better visual representation
    if (startSample === endSample) {
      // Single sample
      if (startSample < waveformData.peaks.length) {
        max = Math.abs(waveformData.peaks[startSample]);
        sampleCount = 1;
      }
    } else {
      // Multiple samples
      for (let j = startSample; j <= endSample && j < waveformData.peaks.length; j++) {
        const sample = Math.abs(waveformData.peaks[j]);
        max = Math.max(max, sample);
        rms += sample * sample;
        sampleCount++;
      }
    }

    if (sampleCount > 0) {
      rms = Math.sqrt(rms / sampleCount);
    }

    // Use a combination of peak and RMS for more natural-looking waveform
    const displayValue = sampleCount > 0 ? Math.max(max * 0.8, rms * 1.2) : 0;
    const barHeight = Math.max(1, displayValue * (height - 8));
    const y = centerY - barHeight / 2;

    // Determine if this bar is in the played or unplayed section
    const isPlayed = x < progressX;

    // Set color based on playback progress
    if (isPlayed) {
      // Played portion - darker blue
      ctx.fillStyle = DEFAULT_COLORS.amplitudePlayed;
    } else {
      // Unplayed portion - original color
      ctx.fillStyle = color;
    }

    // Draw the waveform bar with proper spacing
    ctx.fillRect(x, y, effectiveBarWidth, barHeight);

    // The spacing is automatically handled by using effectiveBarWidth
  }
}

function renderFrequencyWaveform(
  ctx: CanvasRenderingContext2D,
  waveformData: WaveformData,
  width: number,
  height: number,
  centerY: number,
  currentTime: number = 0
): void {
  const { frequencyData } = waveformData;
  if (!frequencyData) return;

  // Calculate progress position for frequency mode too
  const progress = waveformData.duration > 0 ? currentTime / waveformData.duration : 0;
  const progressX = progress * width;

  // Use the same full-width coverage logic as amplitude mode
  const bandsLength = frequencyData.low.length; // All bands should have the same length

  // Always render bars across the full width, scaling the data appropriately
  const barsToRender = Math.min(width, bandsLength * 2); // Allow some upsampling for clarity
  const barWidth = width / barsToRender;
  const effectiveBarWidth = Math.max(0.5, barWidth - 0.5); // Minimum bar width with spacing

  // Calculate how to map from canvas position to frequency data
  const bandsPerPixel = bandsLength / width;

  const bands = [frequencyData.low, frequencyData.midLow, frequencyData.midHigh, frequencyData.high];

  for (let i = 0; i < barsToRender; i++) {
    const x = i * barWidth;
    if (x >= width) break;

    // Map canvas position to frequency data index
    const bandIndex = (x / width) * bandsLength;
    const startSample = Math.floor(bandIndex);
    const endSample = Math.min(Math.ceil(bandIndex + bandsPerPixel), bandsLength - 1);

    // Determine if this bar is in the played or unplayed section
    const isPlayed = x < progressX;

    let totalHeight = 0;

    // Draw each frequency band
    bands.forEach((band, bandIdx) => {
      let max = 0;
      let rms = 0;
      let sampleCount = 0;

      // Calculate both peak and RMS for better visual representation
      if (startSample === endSample) {
        // Single sample
        if (startSample < band.length) {
          max = Math.abs(band[startSample]);
          sampleCount = 1;
        }
      } else {
        // Multiple samples
        for (let j = startSample; j <= endSample && j < band.length; j++) {
          const sample = Math.abs(band[j]);
          max = Math.max(max, sample);
          rms += sample * sample;
          sampleCount++;
        }
      }

      if (sampleCount > 0) {
        rms = Math.sqrt(rms / sampleCount);
      }

      // Calculate display value with enhanced visual prominence
      let displayValue = 0;
      if (sampleCount > 0) {
        // Combine peak and RMS with enhanced scaling for better visual impact
        const combinedValue = Math.max(max * 1.0, rms * 1.4);

        // Apply gentle soft limiting that preserves visual impact
        // More permissive threshold and gentler compression
        const enhancedSoftLimit = (value: number, threshold: number = 1.2) => {
          if (value <= threshold) {
            return value; // Allow higher values before compression
          } else {
            // Very gentle compression above threshold
            const excess = value - threshold;
            const compressed = threshold + (excess * 0.8); // Less aggressive compression
            return Math.min(compressed, 2.0); // Higher ceiling before hard limit
          }
        };

        displayValue = enhancedSoftLimit(combinedValue);
      }

      // Calculate band height with enhanced scaling for better visual prominence
      const maxBandHeight = (height - 12) * 0.35; // Increase max height per band to 35%
      const minBandHeight = 1;
      const scalingFactor = 0.6; // Increase overall scaling factor
      const bandHeight = Math.max(minBandHeight, Math.min(maxBandHeight, displayValue * (height - 12) * scalingFactor));

      // Calculate positions for upper and lower parts
      const upperY = centerY - totalHeight - bandHeight;
      const lowerY = centerY + totalHeight;

      // Set color based on playback progress with enhanced contrast
      if (isPlayed) {
        // Played portion - use darker but still vibrant variants for better visibility
        const playedColors = [
          'rgba(20, 40, 120, 0.95)',   // Enhanced darker low
          'rgba(35, 70, 150, 0.9)',    // Enhanced darker mid-low
          'rgba(50, 90, 180, 0.85)',   // Enhanced darker mid-high
          'rgba(65, 110, 200, 0.8)',   // Enhanced darker high
        ];
        ctx.fillStyle = playedColors[bandIdx];
      } else {
        // Unplayed portion - enhanced original colors for better prominence
        const enhancedColors = [
          'rgba(30, 64, 175, 0.9)',    // Enhanced low
          'rgba(59, 130, 246, 0.85)',  // Enhanced mid-low
          'rgba(96, 165, 250, 0.8)',   // Enhanced mid-high
          'rgba(147, 197, 253, 0.75)', // Enhanced high
        ];
        ctx.fillStyle = enhancedColors[bandIdx];
      }

      // Draw the frequency band bars with proper width
      ctx.fillRect(x, upperY, effectiveBarWidth, bandHeight);
      ctx.fillRect(x, lowerY, effectiveBarWidth, bandHeight);

      // Optimized spacing between bands for enhanced visual separation
      // Reduce spacing slightly to accommodate larger bands while maintaining clarity
      const bandSpacing = Math.max(1, Math.min(3, bandHeight * 0.08 + 1));
      totalHeight += bandHeight * 0.9 + bandSpacing; // Slight overlap for more compact stacking
    });
  }
}

// Calculate seek time from canvas click position
export function calculateSeekTime(
  event: MouseEvent,
  canvas: HTMLCanvasElement,
  duration: number
): number {
  const rect = canvas.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const progress = x / rect.width;
  return Math.max(0, Math.min(duration, progress * duration));
}

// Optimize canvas for high DPI displays
export function setupHighDPICanvas(
  canvas: HTMLCanvasElement,
  width: number,
  height: number
): CanvasRenderingContext2D | null {
  const ctx = canvas.getContext('2d');
  if (!ctx) return null;

  const devicePixelRatio = window.devicePixelRatio || 1;

  canvas.width = width * devicePixelRatio;
  canvas.height = height * devicePixelRatio;
  canvas.style.width = `${width}px`;
  canvas.style.height = `${height}px`;

  ctx.scale(devicePixelRatio, devicePixelRatio);

  return ctx;
}

// Debounce function for resize events
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Performance monitoring utilities
export class WaveformPerformanceMonitor {
  private renderTimes: number[] = [];
  private maxSamples = 100;

  recordRenderTime(startTime: number): void {
    const renderTime = performance.now() - startTime;
    this.renderTimes.push(renderTime);

    if (this.renderTimes.length > this.maxSamples) {
      this.renderTimes.shift();
    }
  }

  getAverageRenderTime(): number {
    if (this.renderTimes.length === 0) return 0;
    return this.renderTimes.reduce((sum, time) => sum + time, 0) / this.renderTimes.length;
  }

  getMaxRenderTime(): number {
    return this.renderTimes.length > 0 ? Math.max(...this.renderTimes) : 0;
  }

  reset(): void {
    this.renderTimes = [];
  }
}

// Validate waveform data
export function validateWaveformData(data: any): data is WaveformData {
  return (
    data &&
    typeof data === 'object' &&
    data.peaks instanceof Float32Array &&
    typeof data.duration === 'number' &&
    typeof data.sampleRate === 'number' &&
    data.duration > 0 &&
    data.sampleRate > 0
  );
}
