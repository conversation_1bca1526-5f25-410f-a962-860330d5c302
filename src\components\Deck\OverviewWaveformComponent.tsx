import React, { useEffect, useRef, useState } from 'react';
import { observer } from 'mobx-react-lite';
import WaveSurfer from 'wavesurfer.js'; // This now points to our local version
import { DeckStoreInstance } from '@/stores/DeckStore';
import { Card } from '@/components/ui/card';
import useWhyDidYouUpdate from '../useWhyDidYouUpdate';


interface WaveformComponentProps {
  deck: DeckStoreInstance;
  height?: number;
}

const WaveformComponent: React.FC<WaveformComponentProps> = observer((props) => {
  const {
    deck,
    height = 80,
  } = props;
  const waveformRef = useRef<HTMLDivElement>(null);
  const wavesurferRef = useRef<WaveSurfer | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isWaveSurferReady, setIsWaveSurferReady] = useState(false); // New state to track if wavesurfer is ready

  useWhyDidYouUpdate(`WaveformComponent (Deck: ${deck.id})`, {
    deck,
    height,
  });

  // Create and destroy WaveSurfer instance
  useEffect(() => {
    
    if (!waveformRef.current) return;
    if (!deck.loadedTrack) return;

    setIsWaveSurferReady(false); // Set to false when creating a new instance
    setIsLoading(true); // Set loading state when creating a new instance

    // Calculate adjusted height for waveform to account for plugins
    let waveformHeight = height;
  
    // Create WaveSurfer instance with type assertion to handle custom options
    const wavesurfer = WaveSurfer.create({
      container: waveformRef.current,
      height: waveformHeight,
      waveColor: 'rgba(100, 100, 100, 0.4)',
      progressColor: 'rgba(100, 100, 255, 0.8)',
      cursorColor: '#fff',
      // barWidth: detailed ? 1 : 2,
      // barGap: detailed ? 0 : 1,
      // barRadius: 2,
      normalize: true,
      interact: true,    
      fillParent: true,
      width: '100%', // Explicitly set width for overview to fill parent
      media: new Audio(), // Use a dummy audio element
      mediaControls: false, // Disable media controls
    } as any); // Use type assertion to bypass TypeScript checking

    waveformRef.current.style.overflow = 'hidden';
    waveformRef.current.style.width = '100%'; // Explicitly set width to fill parent
  
    // Store the instance
    wavesurferRef.current = wavesurfer;

    // Set up event listeners
    wavesurfer.on('ready', () => {
      setIsLoading(false);
      setIsWaveSurferReady(true); // Set to true when wavesurfer is ready
      console.log('Waveform ready');
    });

    wavesurfer.on('error', (err) => {
      
      if (err instanceof MediaError && err.code === 4 && err.message === 'MEDIA_ELEMENT_ERROR: Empty src attribute') {
        // Do nothing
      } else {
        console.error('Waveform error:', err);
        setIsLoading(false);
        setIsWaveSurferReady(false);
      }
    });

    // Handle click events from the waveform visualization
    wavesurfer.on('interaction', (time) => {
      if (deck.loadedTrack) {
        // Use the deck's seek method which will update the audio engine
        deck.seek(time);
      }
    });

    // Load the track if available
    const loadTrack = async () => {
      
      console.log("load track in overview")
      if (deck.loadedTrack && deck.audioEngine) {
        try {
          if (!deck.audioEngine.isBufferLoaded()) {
            await deck.audioEngine.loadTrack(deck.loadedTrack.id);
          }
          
          
          const audioBuffer = deck.audioEngine.getAudioBuffer();
          if (audioBuffer) {
            const peaks = [];
            for (let i = 0; i < audioBuffer.numberOfChannels; i++) {
              peaks.push(audioBuffer.getChannelData(i));
            }
            wavesurfer.load('', peaks, audioBuffer.duration);
          }
        } catch (error) {
          console.error('Error loading track in wavesurfer init:', error);
        }
      }
    };
    
    loadTrack();

    // Clean up on unmount
    return () => {
      
      wavesurfer.destroy();
      wavesurferRef.current = null;
      setIsWaveSurferReady(false); // Set to false on cleanup
    };
  }, [deck.loadedTrack]);

   // Sync current time - always update the Wavesurfer playhead position
  // This is now the primary way we keep the visualization in sync with our audio engine
  useEffect(() => {
    if (!wavesurferRef.current || !deck.loadedTrack) return;

    // Always update the playhead position to match the deck's current time
    // We don't need to check for significant differences anymore since Wavesurfer
    // is not controlling playback and can't cause feedback loops
    wavesurferRef.current.setTime(deck.currentTime);

  }, [deck.currentTime, deck.loadedTrack]);

  // Also sync when playback state changes to ensure visual state matches
  useEffect(() => {
    if (!wavesurferRef.current || !deck.loadedTrack) return;

    // Update the playhead position when playback starts or stops
    wavesurferRef.current.setTime(deck.currentTime);

  }, [deck.isPlaying, deck.loadedTrack]);

  return (
    <Card className="relative overflow-hidden">
      <div className="p-1">
        {/* Waveform container */}
        <div className="relative">
          <div
            ref={waveformRef}
            className={`w-full ${isLoading ? 'opacity-50' : 'opacity-100'} transition-opacity`}
          />
        </div>

        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/50">
            <span className="text-sm text-muted-foreground">Loading waveform...</span>
          </div>
        )}

      </div>
    </Card>
  );
});

export default WaveformComponent;

















