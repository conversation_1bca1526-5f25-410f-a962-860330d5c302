// src/services/AudioManager.ts

/**
 * Manages the shared AudioContext instance for the application.
 * Using a shared context is generally recommended for performance
 * and resource management.
 */

let sharedAudioContext: AudioContext | null = null;

/**
 * Retrieves or creates the shared AudioContext instance.
 * @returns The shared AudioContext instance.
 */
export function getSharedAudioContext(): AudioContext {
    if (!sharedAudioContext) {
        try {
            sharedAudioContext = new AudioContext();
            console.log("Shared AudioContext created successfully.");
            // Optional: Handle state changes (e.g., suspended context)
            sharedAudioContext.onstatechange = () => {
                console.log(`Shared AudioContext state changed to: ${sharedAudioContext?.state}`);
            };
        } catch (error) {
            console.error("Failed to create shared AudioContext:", error);
            // Handle the error appropriately - maybe throw, or return a dummy context?
            // For now, re-throwing might be best to signal a critical failure.
            throw new Error("Could not create AudioContext. Web Audio API might not be supported or enabled.");
        }
    }
    // Optional: Resume context if it's suspended (e.g., due to browser policy)
    if (sharedAudioContext.state === 'suspended') {
        sharedAudioContext.resume().catch(err => console.error("Failed to resume suspended AudioContext:", err));
    }
    return sharedAudioContext;
}

/**
 * Closes the shared AudioContext if it exists.
 * Useful for cleanup during application shutdown or testing.
 */
export function closeSharedAudioContext(): void {
    if (sharedAudioContext) {
        sharedAudioContext.close()
            .then(() => {
                console.log("Shared AudioContext closed.");
                sharedAudioContext = null;
            })
            .catch(err => console.error("Error closing shared AudioContext:", err));
    }
}
