// Web Worker for high-performance waveform processing
// Handles audio analysis and generates optimized waveform data

interface ProcessAudioMessage {
  type: 'process-audio';
  data: {
    channelData: Float32Array[];
    sampleRate: number;
    duration: number;
    waveformStyle: 'amplitude' | 'frequency';
  };
}

interface WaveformData {
  peaks: Float32Array;
  frequencyData?: {
    low: Float32Array;
    midLow: Float32Array;
    midHigh: Float32Array;
    high: Float32Array;
  };
  duration: number;
  sampleRate: number;
}

type WorkerMessage = ProcessAudioMessage;

// FFT implementation for frequency analysis
class FFT {
  private size: number;
  private cosTable: Float32Array;
  private sinTable: Float32Array;

  constructor(size: number) {
    this.size = size;
    this.cosTable = new Float32Array(size / 2);
    this.sinTable = new Float32Array(size / 2);

    for (let i = 0; i < size / 2; i++) {
      const angle = -2 * Math.PI * i / size;
      this.cosTable[i] = Math.cos(angle);
      this.sinTable[i] = Math.sin(angle);
    }
  }

  // Simple FFT implementation for frequency analysis
  transform(real: Float32Array, imag: Float32Array): void {
    const n = this.size;

    // Bit-reversal permutation
    for (let i = 0; i < n; i++) {
      const j = this.reverseBits(i, Math.log2(n));
      if (j > i) {
        [real[i], real[j]] = [real[j], real[i]];
        [imag[i], imag[j]] = [imag[j], imag[i]];
      }
    }

    // Cooley-Tukey FFT
    for (let size = 2; size <= n; size *= 2) {
      const halfSize = size / 2;
      const step = n / size;

      for (let i = 0; i < n; i += size) {
        for (let j = 0; j < halfSize; j++) {
          const u = i + j;
          const v = u + halfSize;
          const twiddle = j * step;

          const tReal = real[v] * this.cosTable[twiddle] - imag[v] * this.sinTable[twiddle];
          const tImag = real[v] * this.sinTable[twiddle] + imag[v] * this.cosTable[twiddle];

          real[v] = real[u] - tReal;
          imag[v] = imag[u] - tImag;
          real[u] += tReal;
          imag[u] += tImag;
        }
      }
    }
  }

  private reverseBits(num: number, bits: number): number {
    let result = 0;
    for (let i = 0; i < bits; i++) {
      result = (result << 1) | (num & 1);
      num >>= 1;
    }
    return result;
  }
}

// Process audio data to generate waveform peaks with optimizations
function generateAmplitudePeaks(channelData: Float32Array[], targetLength: number = 4096): Float32Array {
  // Mix down to mono if stereo
  const monoData = channelData.length > 1
    ? mixToMono(channelData)
    : channelData[0];

  // Always use the full targetLength to ensure waveform spans entire track duration
  // Only reduce if the audio is extremely short
  const actualTargetLength = Math.min(targetLength, Math.max(512, Math.floor(monoData.length / 16)));
  const peaks = new Float32Array(actualTargetLength);
  const samplesPerPeak = monoData.length / actualTargetLength;

  // Use more sophisticated peak detection for better visual quality
  for (let i = 0; i < actualTargetLength; i++) {
    const start = Math.floor(i * samplesPerPeak);
    const end = Math.floor((i + 1) * samplesPerPeak);

    let max = 0;
    let rms = 0;
    let sampleCount = 0;

    // Calculate both peak and RMS for more accurate representation
    for (let j = start; j < end && j < monoData.length; j++) {
      const abs = Math.abs(monoData[j]);
      if (abs > max) max = abs;
      rms += abs * abs;
      sampleCount++;
    }

    if (sampleCount > 0) {
      rms = Math.sqrt(rms / sampleCount);
      // Combine peak and RMS for better visual representation
      // This helps show both transients (peaks) and sustained energy (RMS)
      peaks[i] = Math.max(max * 0.7, rms * 1.3);
    } else {
      peaks[i] = 0;
    }
  }

  return peaks;
}

// Generate frequency-based waveform data
function generateFrequencyData(
  channelData: Float32Array[],
  sampleRate: number,
  targetLength: number = 4096
): {
  low: Float32Array;
  midLow: Float32Array;
  midHigh: Float32Array;
  high: Float32Array;
} {
  const monoData = channelData.length > 1
    ? mixToMono(channelData)
    : channelData[0];

  // Use the same target length logic as amplitude peaks for consistency
  const actualTargetLength = Math.min(targetLength, Math.max(512, Math.floor(monoData.length / 16)));
  const fftSize = 1024;
  const fft = new FFT(fftSize);
  const hopSize = Math.floor(monoData.length / actualTargetLength);

  const low = new Float32Array(actualTargetLength);
  const midLow = new Float32Array(actualTargetLength);
  const midHigh = new Float32Array(actualTargetLength);
  const high = new Float32Array(actualTargetLength);

  // Frequency band boundaries (in Hz)
  const lowFreq = 60;      // 60 Hz
  const midLowFreq = 250;  // 250 Hz
  const midHighFreq = 4000; // 4 kHz
  const highFreq = 16000;  // 16 kHz

  // Convert to bin indices
  const binSize = sampleRate / fftSize;
  const lowBin = Math.floor(lowFreq / binSize);
  const midLowBin = Math.floor(midLowFreq / binSize);
  const midHighBin = Math.floor(midHighFreq / binSize);
  const highBin = Math.floor(highFreq / binSize);

  // Track global maximum for adaptive normalization
  let globalMax = 0;
  const tempResults: Array<{low: number, midLow: number, midHigh: number, high: number}> = [];

  // First pass: calculate all values and find global maximum
  for (let i = 0; i < actualTargetLength; i++) {
    const start = i * hopSize;
    const end = Math.min(start + fftSize, monoData.length);

    if (end - start < fftSize) break;

    // Prepare FFT input
    const real = new Float32Array(fftSize);
    const imag = new Float32Array(fftSize);

    // Copy audio data and apply window function
    for (let j = 0; j < fftSize; j++) {
      if (start + j < monoData.length) {
        // Hanning window
        const window = 0.5 * (1 - Math.cos(2 * Math.PI * j / (fftSize - 1)));
        real[j] = monoData[start + j] * window;
      }
    }

    // Perform FFT
    fft.transform(real, imag);

    // Calculate magnitude spectrum and extract frequency bands
    let lowMag = 0, midLowMag = 0, midHighMag = 0, highMag = 0;
    let lowCount = 0, midLowCount = 0, midHighCount = 0, highCount = 0;

    for (let bin = 1; bin < fftSize / 2; bin++) {
      const magnitude = Math.sqrt(real[bin] * real[bin] + imag[bin] * imag[bin]);

      if (bin >= lowBin && bin < midLowBin) {
        lowMag += magnitude;
        lowCount++;
      } else if (bin >= midLowBin && bin < midHighBin) {
        midLowMag += magnitude;
        midLowCount++;
      } else if (bin >= midHighBin && bin < highBin) {
        midHighMag += magnitude;
        midHighCount++;
      } else if (bin >= highBin) {
        highMag += magnitude;
        highCount++;
      }
    }

    // Calculate average magnitude for each band
    const lowAvg = lowCount > 0 ? lowMag / lowCount : 0;
    const midLowAvg = midLowCount > 0 ? midLowMag / midLowCount : 0;
    const midHighAvg = midHighCount > 0 ? midHighMag / midHighCount : 0;
    const highAvg = highCount > 0 ? highMag / highCount : 0;

    // Store results for adaptive normalization
    const result = { low: lowAvg, midLow: midLowAvg, midHigh: midHighAvg, high: highAvg };
    tempResults.push(result);

    // Track global maximum across all bands
    const maxValue = Math.max(lowAvg, midLowAvg, midHighAvg, highAvg);
    globalMax = Math.max(globalMax, maxValue);
  }

  // Second pass: apply balanced normalization and scaling
  // Use a more conservative adaptive scale to preserve visual impact
  const adaptiveScale = globalMax > 0 ? Math.min(2.0, fftSize * 0.8 / globalMax) : 1.0;

  for (let i = 0; i < tempResults.length; i++) {
    const result = tempResults[i];

    // Apply balanced scaling that preserves dynamics while preventing extreme clipping
    const balancedScale = (value: number) => {
      if (value <= 0) return 0;

      // Apply adaptive normalization with less aggressive compression
      const normalized = (value * adaptiveScale) / (fftSize * 0.4);

      // Use a gentler compression curve that preserves more contrast
      if (normalized <= 0.7) {
        // Linear scaling for most of the range
        return normalized * 1.4; // Boost overall levels
      } else {
        // Gentle compression only for the loudest parts
        const excess = normalized - 0.7;
        const compressed = 0.7 + (excess * 0.6); // Less aggressive compression
        return Math.min(compressed * 1.4, 1.8); // Allow higher peaks but prevent extreme clipping
      }
    };

    // Apply balanced scaling with enhanced frequency-specific adjustments
    low[i] = balancedScale(result.low) * 1.1;      // Boost low frequencies for impact
    midLow[i] = balancedScale(result.midLow) * 1.3;   // Strong mid-low presence
    midHigh[i] = balancedScale(result.midHigh) * 1.4; // Enhanced mid-high visibility
    high[i] = balancedScale(result.high) * 1.6;       // Strong high frequency presence
  }

  return { low, midLow, midHigh, high };
}

// Mix multiple channels to mono
function mixToMono(channelData: Float32Array[]): Float32Array {
  const length = channelData[0].length;
  const mono = new Float32Array(length);

  for (let i = 0; i < length; i++) {
    let sum = 0;
    for (let channel = 0; channel < channelData.length; channel++) {
      sum += channelData[channel][i];
    }
    mono[i] = sum / channelData.length;
  }

  return mono;
}

// Main message handler
self.onmessage = (event: MessageEvent<WorkerMessage>) => {
  const { type, data } = event.data;

  if (type === 'process-audio') {
    try {
      console.time('Waveform Processing');

      const { channelData, sampleRate, duration, waveformStyle } = data;

      // Validate input data
      if (!channelData || channelData.length === 0) {
        throw new Error('No channel data provided');
      }

      if (sampleRate <= 0 || duration <= 0) {
        throw new Error('Invalid sample rate or duration');
      }

      // Generate amplitude peaks (always needed)
      const peaks = generateAmplitudePeaks(channelData);

      let waveformData: WaveformData = {
        peaks,
        duration,
        sampleRate,
      };

      // Generate frequency data if needed
      if (waveformStyle === 'frequency') {
        try {
          const frequencyData = generateFrequencyData(channelData, sampleRate);
          waveformData.frequencyData = frequencyData;
        } catch (freqError) {
          console.warn('Failed to generate frequency data, falling back to amplitude:', freqError);
          // Continue with amplitude-only data
        }
      }

      console.timeEnd('Waveform Processing');

      // Send result back to main thread
      self.postMessage({
        type: 'waveform-ready',
        data: waveformData,
      });

    } catch (error) {
      console.error('Waveform processing error:', error);
      self.postMessage({
        type: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
};

// Export for TypeScript
export {};
