// src/services/AudioEngine.ts
import { getSharedAudioContext } from './AudioManager';
import { RootStoreType } from '@/stores/RootStore';
import { DeckStoreInstance } from '@/stores/DeckStore';
import { getTrackFileHandle } from './AudioAnalysisService';

/**
 * Interface for audio buffer cache entries
 */
interface AudioBufferCacheEntry {
  buffer: AudioBuffer;
  lastAccessed: number;
}

/**
 * Class that manages the Web Audio API playback for a single deck
 */
export class DeckAudioEngine {
  private audioContext: AudioContext;
  private rootStore: RootStoreType;
  private deckStore: DeckStoreInstance;

  // Audio nodes
  private sourceNode: AudioBufferSourceNode | null = null;
  private gainNode: GainNode | null = null; // Deck's main volume
  private crossfaderGainNode: GainNode | null = null; // For crossfader
  private lowEQNode: BiquadFilterNode | null = null;
  private midEQNode: BiquadFilterNode | null = null; // Used in 3-band mode
  private midLoEQNode: BiquadFilterNode | null = null; // Used in 4-band mode
  private midHiEQNode: BiquadFilterNode | null = null; // Used in 4-band mode
  private highEQNode: BiquadFilterNode | null = null;

  // Phase Vocoder node for master tempo
  private phaseVocoderNode: AudioWorkletNode | null = null;
  private phaseVocoderGainCompensation: GainNode | null = null;
  private isPhaseVocoderInitialized: boolean = false;

  // Playback state
  private audioBuffer: AudioBuffer | null = null;
  private playbackStartTime: number = 0;
  private playbackOffset: number = 0;
  private isPlaying: boolean = false;

  // Animation frame for time updates
  private animationFrameId: number | null = null;

  // Static cache for audio buffers (shared across all deck instances)
  private static audioBufferCache: Map<string, AudioBufferCacheEntry> = new Map();
  private static MAX_CACHE_SIZE = 10; // Maximum number of buffers to keep in cache

  // Track currently being loaded
  private _loadingTrackPromise: { [trackId: string]: Promise<void> | null } = {};

  constructor(rootStore: RootStoreType, deckStore: DeckStoreInstance) {
    this.rootStore = rootStore;
    this.deckStore = deckStore;
    this.audioContext = getSharedAudioContext();

    // Initialize audio nodes
    this.setupAudioNodes();
  }

  /**
   * Connect the EQ nodes based on the current EQ mode (3-band or 4-band)
   * and whether per-band crossfaders are enabled
   */
  private connectEQNodes(): void {
    // Disconnect all EQ nodes first to avoid duplicate connections
    if (this.lowEQNode) this.lowEQNode.disconnect();
    if (this.midEQNode) this.midEQNode.disconnect();
    if (this.midLoEQNode) this.midLoEQNode.disconnect();
    if (this.midHiEQNode) this.midHiEQNode.disconnect();
    if (this.highEQNode) this.highEQNode.disconnect();

    // Connect the EQ chain based on the EQ mode
    if (this.rootStore.settingsStore.eqBands === "3-band") {
      // 3-band mode: low -> mid -> high -> gain
      if (this.lowEQNode && this.midEQNode) this.lowEQNode.connect(this.midEQNode);
      if (this.midEQNode && this.highEQNode) this.midEQNode.connect(this.highEQNode);
      if (this.highEQNode && this.gainNode) this.highEQNode.connect(this.gainNode);

      console.log("DeckAudioEngine: Connected EQ nodes in 3-band mode");
    } else {
      // 4-band mode: low -> mid-lo -> mid-hi -> high -> gain
      if (this.lowEQNode && this.midLoEQNode) this.lowEQNode.connect(this.midLoEQNode);
      if (this.midLoEQNode && this.midHiEQNode) this.midLoEQNode.connect(this.midHiEQNode);
      if (this.midHiEQNode && this.highEQNode) this.midHiEQNode.connect(this.highEQNode);
      if (this.highEQNode && this.gainNode) this.highEQNode.connect(this.gainNode);

      console.log("DeckAudioEngine: Connected EQ nodes in 4-band mode");
    }
  }

  /**
   * Set up the audio processing chain
   */
  private setupAudioNodes(): void {
    // Create gain node for volume control
    this.gainNode = this.audioContext.createGain();
    this.gainNode.gain.value = 0.7; // Default volume (same as in WaveformComponent)

    // Create crossfader gain node
    this.crossfaderGainNode = this.audioContext.createGain();
    this.crossfaderGainNode.gain.value = 1.0; // Default gain for crossfader

    // Create EQ nodes with frequencies from settings
    this.lowEQNode = this.audioContext.createBiquadFilter();
    this.lowEQNode.type = 'lowshelf';
    this.lowEQNode.frequency.value = this.rootStore.settingsStore.lowEQFrequency;
    this.lowEQNode.Q.value = 1;
    this.lowEQNode.gain.value = 0;

    // Create mid EQ node for 3-band mode
    this.midEQNode = this.audioContext.createBiquadFilter();
    this.midEQNode.type = 'peaking';
    this.midEQNode.frequency.value = this.rootStore.settingsStore.midEQFrequency;
    this.midEQNode.Q.value = 1;
    this.midEQNode.gain.value = 0;

    // Create mid-lo and mid-hi EQ nodes for 4-band mode
    this.midLoEQNode = this.audioContext.createBiquadFilter();
    this.midLoEQNode.type = 'peaking';
    this.midLoEQNode.frequency.value = this.rootStore.settingsStore.midLoEQFrequency;
    this.midLoEQNode.Q.value = 1;
    this.midLoEQNode.gain.value = 0;

    this.midHiEQNode = this.audioContext.createBiquadFilter();
    this.midHiEQNode.type = 'peaking';
    this.midHiEQNode.frequency.value = this.rootStore.settingsStore.midHiEQFrequency;
    this.midHiEQNode.Q.value = 1;
    this.midHiEQNode.gain.value = 0;

    this.highEQNode = this.audioContext.createBiquadFilter();
    this.highEQNode.type = 'highshelf';
    this.highEQNode.frequency.value = this.rootStore.settingsStore.highEQFrequency;
    this.highEQNode.Q.value = 1;
    this.highEQNode.gain.value = 0;

    // Connect the EQ chain based on the EQ mode (3-band or 4-band)
    this.connectEQNodes();

    // Connect the gain node to the crossfader gain node, then to destination
    // EQ Chain -> gainNode (deck volume) -> crossfaderGainNode -> destination
    if (this.gainNode && this.crossfaderGainNode) {
      this.gainNode.connect(this.crossfaderGainNode);
      this.crossfaderGainNode.connect(this.audioContext.destination);
    }


    // // Initialize Phase Vocoder worklet immediately and ensure it's ready
    // DROR - doubt this is needed as it happens in play() anyway
    // this.initPhaseVocoderWorklet().then(() => {
    //   console.log('Phase Vocoder worklet initialized in setup');
    //   // If we're already playing with a non-1.0 playback rate and master tempo is enabled,
    //   // we need to reapply the playback rate to use the newly initialized Phase Vocoder
    //   if (this.isPlaying && this.deckStore.masterTempo && this.deckStore.playbackRate !== 1.0) {
    //     this.setPlaybackRate(this.deckStore.playbackRate);
    //   }
    // }).catch(err => {
    //   console.error('Failed to initialize Phase Vocoder worklet in setup:', err);
    // });
  }

  /**
   * Initialize the Phase Vocoder worklet for master tempo
   */
  private async initPhaseVocoderWorklet(): Promise<void> {
    try {
      // Check if the worklet is already initialized
      if (this.isPhaseVocoderInitialized) {
        return;
      }

      // Add the Phase Vocoder worklet module to the audio context
      await this.audioContext.audioWorklet.addModule('/lib/phaze/www/phase-vocoder.js');

      this.isPhaseVocoderInitialized = true;
      console.log('Phase Vocoder worklet initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Phase Vocoder worklet:', error);
      this.isPhaseVocoderInitialized = false;
    }
  }

  /**
   * Check if audio buffer is loaded
   */
  public isBufferLoaded(): boolean {
    return this.audioBuffer !== null;
  }

  /**
   * Load a track's audio buffer
   */
  public async loadTrack(trackId: string): Promise<void> {
    // If this track is already being loaded, return the existing promise
    if (this._loadingTrackPromise[trackId]) {
      console.log(`DeckAudioEngine: Already loading track ${trackId}, returning existing promise`);
      return this._loadingTrackPromise[trackId];
    }

    // Create a new loading promise for this track
    this._loadingTrackPromise[trackId] = this._loadTrackInternal(trackId);

    try {
      // Wait for the loading to complete
      await this._loadingTrackPromise[trackId];
    } finally {
      // Clean up the promise reference when done
      delete this._loadingTrackPromise[trackId];
    }
  }

  /**
   * Internal method to load a track's audio buffer
   */
  private async _loadTrackInternal(trackId: string): Promise<void> {
    // Check if we already have this buffer in cache
    if (DeckAudioEngine.audioBufferCache.has(trackId)) {
      const cacheEntry = DeckAudioEngine.audioBufferCache.get(trackId)!;
      this.audioBuffer = cacheEntry.buffer;
      cacheEntry.lastAccessed = Date.now();
      console.log(`DeckAudioEngine: Loaded track ${trackId} from cache`);
      return;
    }

    // Make sure rootStore is defined before proceeding
    if (!this.rootStore) {
      throw new Error('Root store is undefined in DeckAudioEngine');
    }

    // Get file handle
    const fileHandle = await getTrackFileHandle(this.rootStore, trackId);
    if (!fileHandle) {
      throw new Error(`Could not retrieve file handle for track ${trackId}`);
    }

    // Get file and decode audio
    const file = await fileHandle.getFile();
    const arrayBuffer = await file.arrayBuffer();

    // Decode audio data
    console.log(`DeckAudioEngine: Decoding audio data for track ${trackId}...`);
    this.audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
    console.log(`DeckAudioEngine: Successfully decoded audio for track ${trackId}, duration: ${this.audioBuffer.duration}s`);

    // Add to cache
    DeckAudioEngine.audioBufferCache.set(trackId, {
      buffer: this.audioBuffer,
      lastAccessed: Date.now()
    });

    // Manage cache size
    this.manageCache();

    console.log(`DeckAudioEngine: Loaded track ${trackId}`);
  }

  /**
   * Manage the size of the audio buffer cache
   */
  private manageCache(): void {
    if (DeckAudioEngine.audioBufferCache.size <= DeckAudioEngine.MAX_CACHE_SIZE) {
      return;
    }

    // Find the least recently accessed entry
    let oldestKey: string | null = null;
    let oldestTime = Infinity;

    DeckAudioEngine.audioBufferCache.forEach((entry, key) => {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    });

    // Remove the oldest entry
    if (oldestKey) {
      DeckAudioEngine.audioBufferCache.delete(oldestKey);
      console.log(`DeckAudioEngine: Removed track ${oldestKey} from cache`);
    }
  }

  /**
   * Sets up the audio chain for master tempo playback using the Phase Vocoder.
   * This includes initializing the worklet if necessary, creating nodes, and connecting them.
   * @returns {Promise<boolean>} True if setup was successful and play() should call startPlayback(),
   *                            false if playback was handled by fallback or an error occurred.
   */
  private async _setupMasterTempoAudioChain(): Promise<boolean> {
    // Make sure Phase Vocoder is initialized
    if (!this.isPhaseVocoderInitialized) {
      console.log('Phase Vocoder not initialized yet, initializing now...');
      try {
        await this.initPhaseVocoderWorklet();
        console.log('Phase Vocoder initialized for playback');
      } catch (err) {
        console.error('Failed to initialize Phase Vocoder for playback:', err);
        return false; // Signal that play() should not call startPlayback() again and should return
      }
    }

    // Use Phase Vocoder for time-stretching without pitch change
    console.log(`DeckAudioEngine: Using Phase Vocoder for master tempo at rate ${this.deckStore.playbackRate}`);

    // Create a Phase Vocoder node
    this.phaseVocoderNode = new AudioWorkletNode(this.audioContext, 'phase-vocoder-processor');
    this.phaseVocoderGainCompensation = this.audioContext.createGain();
    this.phaseVocoderGainCompensation.gain.value = 2.69; // DROR - this is the gain compensation factor

    // Set pitchFactor to the desired playback rate to change speed without affecting pitch
    const pitchFactorParam = this.phaseVocoderNode.parameters.get('pitchFactor');
    if (pitchFactorParam) {
      pitchFactorParam.value = 1.0 / this.deckStore.playbackRate;
    }

    this.sourceNode!.connect(this.phaseVocoderNode);
    this.phaseVocoderNode.connect(this.phaseVocoderGainCompensation);
    this.phaseVocoderGainCompensation.connect(this.lowEQNode!);
    console.log('Connected audio nodes with Phase Vocoder');
    return true; // Signal that play() should proceed to call startPlayback()
  }
  /**
   * Play the loaded audio buffer
   */
  public async play(): Promise<void> {
    console.log("DeckAudioEngine: play called");
    if (!this.audioBuffer) {
      console.warn('Cannot play: No audio buffer loaded');
      return;
    }

    // If already playing, do nothing
    if (this.isPlaying) return;

    try {
      // Create a new source node (they can only be used once)
      this.sourceNode = this.audioContext.createBufferSource();
      this.sourceNode.buffer = this.audioBuffer;
      this.sourceNode.playbackRate.value = this.deckStore.playbackRate;

      // Check if we should use master tempo
      const useMasterTempo = this.deckStore.masterTempo && this.deckStore.playbackRate !== 1.0;

      if (useMasterTempo) {
        const successfullySetupMasterTempo = await this._setupMasterTempoAudioChain();
        if (!successfullySetupMasterTempo) {
          // Fall back to normal playback if Phase Vocoder fails
          this.sourceNode!.connect(this.lowEQNode!);
        }
        // If successfullySetupMasterTempo is true, the audio chain is set up for master tempo.
        // Proceed to startPlayback() below.
      } else {
        this.sourceNode.connect(this.lowEQNode!);
        // Clean up any existing Phase Vocoder nodes if we're not using master tempo
        if (this.phaseVocoderNode) {
          this.phaseVocoderNode.disconnect();
          this.phaseVocoderNode = null;
        }
        if (this.phaseVocoderGainCompensation) {
          this.phaseVocoderGainCompensation.disconnect();
          this.phaseVocoderGainCompensation = null;
        }
      }
      this.startPlayback();
    } catch (error) {
      console.error('Error starting playback:', error);
    }
  }

  /**
   * Helper method to start the actual playback after nodes are connected
   */
  private startPlayback(): void {
    // Calculate start time and offset
    this.playbackOffset = this.deckStore.currentTime;
    this.playbackStartTime = this.audioContext.currentTime;

    // Start playback from the current position
    this.sourceNode!.start(0, this.playbackOffset);

    // Update state
    this.isPlaying = true;

    // Start the time update loop
    this.startTimeUpdateLoop();

    console.log(`DeckAudioEngine: Started playback at ${this.playbackOffset}s with buffer duration ${this.audioBuffer!.duration}s`);
  }

  /**
   * Pause playback
   */
  public pause(): void {
    console.log(`DeckAudioEngine: Pausing playback at ${this.deckStore.currentTime}s`);
    if (!this.isPlaying || !this.sourceNode) {
      return;
    }

    // Stop the source node
    this.sourceNode.stop();
    this.sourceNode = null;

    // Update the current time based on how long we've been playing
    const elapsedTime = (this.audioContext.currentTime - this.playbackStartTime) * this.deckStore.playbackRate;
    this.deckStore.setCurrentTime(this.playbackOffset + elapsedTime);

    // Update state
    this.isPlaying = false;

    // Stop the time update loop
    this.stopTimeUpdateLoop();

    console.log(`DeckAudioEngine: Paused playback at ${this.deckStore.currentTime}s`);
  }

  /**
   * Seek to a specific time
   */
  public seek(timeInSeconds: number): void {
    const wasPlaying = this.isPlaying

    this.pause();

    // Update the current time
    this.deckStore.setCurrentTime(timeInSeconds);
    this.playbackOffset = timeInSeconds;

    if(wasPlaying) {
      this.play();
    }

    console.log(`DeckAudioEngine: Seeked to ${timeInSeconds}s`);
  }

  /**
   * Set the volume for the deck's main gain node
   */
  public setVolume(volume: number): void {
    if (this.gainNode) {
      this.gainNode.gain.value = volume;
    }
  }

  /**
   * Set the gain for the main crossfader node
   */
  public setCrossfaderGain(gain: number): void {
    if (this.crossfaderGainNode) {
      // Clamp gain value between 0 and 1
      this.crossfaderGainNode.gain.value = Math.max(0, Math.min(1, gain));
    }
  }

  public setBandCrossfaderGain(low: number, mid: number, high: number, midLo: number, midHi: number,
    lowGain: number, midGain: number, highGain: number, midLoGain: number, midHiGain: number): void {
    // assumes the crossfader values are between 0 and 1 and the result of 0 should be an extra -12db - if we are doing full kill it will be handled by setEQ
    this.setEQ(low, mid, high, midLo, midHi,
      (lowGain-1)*12, (midGain-1)*12, (highGain-1)*12, (midLoGain-1)*12, (midHiGain-1)*12);
  }

  /**
   * Set the playback rate
   */
  public setPlaybackRate(rate: number): void {
    console.log(`DeckAudioEngine: Setting playback rate to ${rate}`);

    // If we're currently playing, we need to restart playback with the new settings
    if (this.isPlaying) {
      // Remember current position
      const currentTime = this.deckStore.currentTime;

      // Stop current playback
      this.pause();

      // Start playback again (which will apply the new rate with or without SoundTouch)
      this.play().then(() => {
        // Ensure we're at the same position
        if (Math.abs(this.deckStore.currentTime - currentTime) > 0.1) {
          this.seek(currentTime);
        }
      });

      return;
    }
  }

  /**
   * Set EQ values for 3-band or 4-band mode
   */
  public setEQ(low: number, mid: number, high: number, midLo?: number, midHi?: number, 
               crossfaderLow: number = 0, crossfaderMid: number = 0, crossfaderHigh: number = 0, crossfaderMidLo: number = 0, crossfaderMidHi: number = 0): void {
    // Check if full kill is enabled
    const equalizerFullKill = this.rootStore.settingsStore.equalizerFullKill;
    const is4BandMode = this.rootStore.settingsStore.eqBands === "4-band";

    // Set low EQ
    if (this.lowEQNode) {
      low = low + crossfaderLow;
      // If full kill is enabled and the value is at minimum, set to -100 representing -Infinity
      if (equalizerFullKill && low < 0) {
        this.lowEQNode.gain.value = (low/-12)*-50;
      } else {
        this.lowEQNode.gain.value = low;
      }
    }

    if (is4BandMode) {
      // 4-band mode: use midLo and midHi
      midLo = midLo||0 + crossfaderMidLo;
      midHi = midHi||0 + crossfaderMidHi;
      if (this.midLoEQNode && midLo !== undefined) {
        if (equalizerFullKill && midLo < 0) {
          this.midLoEQNode.gain.value = (midLo/-12)*-50;
        } else {
          this.midLoEQNode.gain.value = midLo;
        }
      }

      if (this.midHiEQNode && midHi !== undefined) {
        if (equalizerFullKill && midHi < 0) {
          this.midHiEQNode.gain.value = (midHi/-12)*-50;
        } else {
          this.midHiEQNode.gain.value = midHi;
        }
      }
    } else {
      // 3-band mode: use mid
      mid = mid + crossfaderMid;
      if (this.midEQNode) {
        if (equalizerFullKill && mid < 0) {
          this.midEQNode.gain.value = (mid/-12)*-50;
        } else {
          this.midEQNode.gain.value = mid;
        }
      }
    }

    // Set high EQ
    high = high + crossfaderHigh;
    if (this.highEQNode) {
      if (equalizerFullKill && high < 0) {
        this.highEQNode.gain.value = (high/-12)*-50;
      } else {
        this.highEQNode.gain.value = high;
      }
    }
    console.log(`DeckAudioEngine: Set EQ values to Low: ${low}, Mid: ${mid}, High: ${high}, Mid-Lo: ${midLo}, Mid-Hi: ${midHi}`);
  }

  /**
   * Update EQ frequencies based on settings
   */
  public updateEQFrequencies(): void {
    const is4BandMode = this.rootStore.settingsStore.eqBands === "4-band";

    // Update EQ node frequencies
    if (this.lowEQNode) {
      this.lowEQNode.frequency.value = this.rootStore.settingsStore.lowEQFrequency;
    }

    if (is4BandMode) {
      // 4-band mode: update midLo and midHi
      if (this.midLoEQNode) {
        this.midLoEQNode.frequency.value = this.rootStore.settingsStore.midLoEQFrequency;
      }

      if (this.midHiEQNode) {
        this.midHiEQNode.frequency.value = this.rootStore.settingsStore.midHiEQFrequency;
      }
    } else {
      // 3-band mode: update mid
      if (this.midEQNode) {
        this.midEQNode.frequency.value = this.rootStore.settingsStore.midEQFrequency;
      }
    }

    if (this.highEQNode) {
      this.highEQNode.frequency.value = this.rootStore.settingsStore.highEQFrequency;
    }

    // Reconnect the EQ nodes in case the EQ mode has changed
    this.connectEQNodes();

    if (is4BandMode) {
      console.log(`DeckAudioEngine: Updated EQ frequencies to Low: ${this.rootStore.settingsStore.lowEQFrequency}Hz, Mid-Lo: ${this.rootStore.settingsStore.midLoEQFrequency}Hz, Mid-Hi: ${this.rootStore.settingsStore.midHiEQFrequency}Hz, High: ${this.rootStore.settingsStore.highEQFrequency}Hz`);
    } else {
      console.log(`DeckAudioEngine: Updated EQ frequencies to Low: ${this.rootStore.settingsStore.lowEQFrequency}Hz, Mid: ${this.rootStore.settingsStore.midEQFrequency}Hz, High: ${this.rootStore.settingsStore.highEQFrequency}Hz`);
    }
  }

  /**
   * Start the loop that updates the current time
   */
  private startTimeUpdateLoop(): void {
    // Stop any existing loop
    this.stopTimeUpdateLoop();

    // Start a new loop
    const updateTime = () => {
      if (!this.isPlaying) return;

      // Calculate the current time based on the audio context time
      const elapsedTime = (this.audioContext.currentTime - this.playbackStartTime) * this.deckStore.playbackRate;
      const currentTime = this.playbackOffset + elapsedTime;

      // Update the deck store
      this.deckStore.setCurrentTime(currentTime);

      // Check if we've reached the end of the track
      if (this.audioBuffer && currentTime >= this.audioBuffer.duration) {
        this.pause();
        this.deckStore.setCurrentTime(0);
        return;
      }

      // Continue the loop
      this.animationFrameId = requestAnimationFrame(updateTime);
    };

    // Start the loop
    this.animationFrameId = requestAnimationFrame(updateTime);
  }

  /**
   * Stop the time update loop
   */
  private stopTimeUpdateLoop(): void {
    if (this.animationFrameId !== null) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  /**
   * Clean up resources
   */
  public dispose(): void {
    this.stopTimeUpdateLoop();

    if (this.sourceNode) {
      this.sourceNode.stop();
      this.sourceNode.disconnect();
      this.sourceNode = null;
    }

    if (this.phaseVocoderNode) {
      this.phaseVocoderNode.disconnect();
      this.phaseVocoderNode = null;
    }

    if (this.phaseVocoderGainCompensation) {
      this.phaseVocoderGainCompensation.disconnect();
      this.phaseVocoderGainCompensation = null;
    }

    if (this.gainNode) {
      this.gainNode.disconnect();
      this.gainNode = null;
    }

    if (this.crossfaderGainNode) {
      this.crossfaderGainNode.disconnect();
      this.crossfaderGainNode = null;
    }

    if (this.lowEQNode) {
      this.lowEQNode.disconnect();
    }

    if (this.midEQNode) {
      this.midEQNode.disconnect();
      this.midEQNode = null;
    }

    if (this.midLoEQNode) {
      this.midLoEQNode.disconnect();
      this.midLoEQNode = null;
    }

    if (this.midHiEQNode) {
      this.midHiEQNode.disconnect();
      this.midHiEQNode = null;
    }

    if (this.highEQNode) {
      this.highEQNode.disconnect();
      this.highEQNode = null;
    }

    this.audioBuffer = null;
    this.isPlaying = false;

    console.log(`DeckAudioEngine: Disposed`);
  }

  /**
   * Debug method to check audio context state
   */
  public debugAudioState(): void {
    console.log({
      audioContextState: this.audioContext.state,
      hasAudioBuffer: this.audioBuffer !== null,
      bufferDuration: this.audioBuffer ? this.audioBuffer.duration : 'N/A',
      isPlaying: this.isPlaying,
      deckCurrentTime: this.deckStore.currentTime,
      playbackOffset: this.playbackOffset,
      sourceNodeExists: this.sourceNode !== null
    });
  }

  /**
   * Load audio buffer directly from a File or Blob
   * This is a fallback method when the track file handle can't be retrieved
   */
  public async loadAudioFromFile(file: File | Blob): Promise<void> {
    try {
      console.log(`DeckAudioEngine: Loading audio directly from file`);

      // Get array buffer from file
      const arrayBuffer = await file.arrayBuffer();

      // Decode audio data
      console.log(`DeckAudioEngine: Decoding audio data from file...`);
      this.audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
      console.log(`DeckAudioEngine: Successfully decoded audio from file, duration: ${this.audioBuffer.duration}s`);

      // We don't add this to cache since we don't have a trackId

      return;
    } catch (error) {
      console.error('DeckAudioEngine: Error loading audio from file:', error);
      this.audioBuffer = null;
      throw error;
    }
  }

  /**
   * Get the current audio buffer
   */
  public getAudioBuffer(): AudioBuffer | null {
    return this.audioBuffer;
  }

  /**
   * Get the duration of the current audio buffer in seconds
   */
  public getDuration(): number {
    return this.audioBuffer ? this.audioBuffer.duration : 0;
  }
}
