import Dexie, { Table } from 'dexie';

// --- Interfaces for Database Tables ---

// --- Interfaces for Database Tables ---

// Added isHidden and analysisStatus
export interface TrackInfo {
  id: string; // Unique identifier (e.g., combination of directory path + filename)
  filePath: string; // Relative path within its tracked directory
  filename: string;
  duration?: number; // In seconds
  artist?: string;
  title?: string;
  album?: string;
  genre?: string;
  rating?: number; // Example: 0-5 stars
  addedDate: number; // Timestamp when added
  lastPlayedDate?: number; // Timestamp when last played
  isHidden: 0 | 1; // Flag to hide track from main list (0 for visible, 1 for hidden)
  analysisStatus: "Pending" | "Analyzing" | "Analyzed" | "Error"; // Analysis progress
  fileHash?: string; // SHA-256 hash of the file content
  duplicateOf?: string; // ID of the original track if this is a duplicate
  directoryId?: string; // Foreign key to the StoredDirectoryHandle.id this track belongs to
  // Add other relevant metadata fields
}

export interface TrackAnalysis {
  trackId: string; // Foreign key to TrackInfo.id
  bpm?: number;
  key?: string; // e.g., "11B" or "Am"
  firstBeatTime?: number; // Time of the first detected beat in seconds
  beatGridTimes?: number[]; // Array of beat timestamps in seconds
  waveformPeaks?: number[]; // Simplified waveform data for preview
  analysisDate: number; // Timestamp of analysis

  // External analysis data from allin1 Python package
  externalAnalysis?: {
    bpm?: number;
    beats?: number[]; // Array of beat timestamps in seconds
    downbeats?: number[]; // Array of downbeat timestamps in seconds
    beat_positions?: number[]; // Array of beat positions (1, 2, 3, 4, 1, 2, 3, 4, etc.)
    segments?: Array<{
      start: number;
      end: number;
      label: string;
    }>;
  };
  externalAnalysisDate?: number; // Timestamp when external analysis was imported
}

export interface TrackCues {
  trackId: string; // Foreign key to TrackInfo.id
  hotCues: (number | null)[]; // Array of hot cue times in seconds (null if unset)
  // Add loop points, memory cues etc. if needed later
}

export interface AppSetting {
  key: string; // Setting name (e.g., "masterVolume", "defaultPitchRange")
  value: any; // Setting value
}

export interface MidiMapping {
  id: string; // Unique ID for the mapping (e.g., control name + device ID)
  deviceName: string; // Name of the MIDI device
  controlIdentifier: string; // e.g., "channel_1_volume", "deck_a_play"
  midiMessage: {
    type: 'noteon' | 'noteoff' | 'controlchange';
    channel: number;
    noteOrControl: number; // Note number or CC number
  };
  actionTarget: string; // Identifier for the action to trigger (e.g., "rootStore.decks[0].setVolume")
  // Add value mapping options if needed (e.g., linear, logarithmic, range)
}

// Interface for storing directory handles
export interface StoredDirectoryHandle {
    id: string; // Unique ID for the tracked directory (e.g., a UUID)
    name: string; // User-friendly name or directory name
    path: string; // The full path of the directory for identification
    handle: FileSystemDirectoryHandle; // The actual handle
    isActive: 0 | 1; // Flag to indicate if the directory is currently tracked
}


// --- Dexie Database Class ---

export class AppDatabase extends Dexie {
  // Declare tables
  tracks!: Table<TrackInfo, string>; // Primary key is 'id' (string)
  analysis!: Table<TrackAnalysis, string>; // Primary key is 'trackId' (string)
  cues!: Table<TrackCues, string>; // Primary key is 'trackId' (string)
  settings!: Table<AppSetting, string>; // Primary key is 'key' (string)
  midiMappings!: Table<MidiMapping, string>; // Primary key is 'id' (string)
  trackedDirectories!: Table<StoredDirectoryHandle, string>; // Primary key is 'id' (string)

  constructor() {
    super('MismoDJDatabase'); // Database name
    this.version(1).stores({
      // Define schema for version 1 - Added isHidden, analysisStatus, rating, filePath to tracks index
      // Added trackedDirectories table
      tracks: 'id, filePath, isHidden, analysisStatus, artist, genre, rating, addedDate, lastPlayedDate',
      analysis: 'trackId, analysisDate', // Primary key 'trackId'
      cues: 'trackId', // Primary key 'trackId'
      settings: 'key', // Primary key 'key'
      midiMappings: 'id, deviceName, controlIdentifier', // Primary key 'id', index others
      trackedDirectories: 'id, name', // Add 'name' to the index
    });
    this.version(2).stores({
        // Version 2: Added fileHash and duplicateOf fields, and index for fileHash
        tracks: 'id, filePath, isHidden, analysisStatus, artist, genre, rating, addedDate, lastPlayedDate, fileHash, duplicateOf',
        // Keep other tables the same unless they also need changes
        analysis: 'trackId, analysisDate',
        cues: 'trackId',
        settings: 'key',
        midiMappings: 'id, deviceName, controlIdentifier',
        trackedDirectories: 'id, name', // Keep 'name' in the index
    });
    this.version(3).stores({
        // Version 3: Added isActive and path to trackedDirectories, indexed them
        tracks: 'id, filePath, isHidden, analysisStatus, artist, genre, rating, addedDate, lastPlayedDate, fileHash, duplicateOf',
        analysis: 'trackId, analysisDate',
        cues: 'trackId',
        settings: 'key',
        midiMappings: 'id, deviceName, controlIdentifier',
        trackedDirectories: 'id, name, path, isActive' // Add 'name' to the index
    }).upgrade(tx => {
        // Add default values for existing directories during upgrade
        return tx.table('trackedDirectories').toCollection().modify(dir => {
            if (dir.isActive === undefined) {
                dir.isActive = true; // Assume existing are active
            }
            // We might not have the path readily available here during upgrade.
            // The path might need to be populated later, perhaps during the first scan after upgrade.
            // Or prompt the user to re-select folders if paths are missing.
            if (dir.path === undefined) {
                dir.path = `unknown_path_${dir.id}`; // Placeholder
                console.warn(`Tracked directory ${dir.id} (${dir.name}) needs path updated.`);
            }
        });
    });
    this.version(4).stores({
      tracks: 'id, filePath, isHidden, analysisStatus, artist, genre, rating, addedDate, lastPlayedDate, fileHash, duplicateOf',
      analysis: 'trackId, analysisDate',
      cues: 'trackId',
      settings: 'key',
      midiMappings: 'id, deviceName, controlIdentifier',
      trackedDirectories: 'id, name, path, isActive' // Ensure name is indexed
    });
    this.version(5).stores({
        // Version 5: Added directoryId to tracks table and index
        tracks: 'id, filePath, isHidden, analysisStatus, artist, genre, rating, addedDate, lastPlayedDate, fileHash, duplicateOf, directoryId',
        // Keep other tables the same
        analysis: 'trackId, analysisDate',
        cues: 'trackId',
        settings: 'key',
        midiMappings: 'id, deviceName, controlIdentifier',
        trackedDirectories: 'id, name, path, isActive'
    });
    this.version(6).stores({
        // Version 6: Added support for external analysis data
        tracks: 'id, filePath, isHidden, analysisStatus, artist, genre, rating, addedDate, lastPlayedDate, fileHash, duplicateOf, directoryId',
        analysis: 'trackId, analysisDate, externalAnalysisDate', // Added externalAnalysisDate index
        cues: 'trackId',
        settings: 'key',
        midiMappings: 'id, deviceName, controlIdentifier',
        trackedDirectories: 'id, name, path, isActive'
    });
    // Add upgrade logic here if needed between versions

    // --- Pre-populate default settings if they don't exist ---
    this.on('populate', async () => {
      await this.settings.bulkPut([
        { key: 'masterVolume', value: 0.8 },
        { key: 'defaultPitchRange', value: 8 }, // e.g., +/- 8%
        { key: 'equalizerFullKill', value: false }, // Default to standard EQ behavior
        { key: 'eqBands', value: '3-band' }, // Default to 3-band EQ
        { key: 'eqPreset', value: 'Custom' }, // Default to custom preset
        { key: 'lowEQFrequency', value: 200 }, // Default low EQ frequency
        { key: 'midEQFrequency', value: 1600 }, // Default mid EQ frequency (for 3-band)
        { key: 'midLoEQFrequency', value: 350 }, // Default mid-lo EQ frequency (for 4-band)
        { key: 'midHiEQFrequency', value: 2500 }, // Default mid-hi EQ frequency (for 4-band)
        { key: 'customEQPresets', value: "[]" }, // Default empty array for custom EQ presets
        { key: 'lastSelectedEQPreset', value: "Custom" }, // Default last selected EQ preset
        { key: 'highEQFrequency', value: 6500 }, // Default high EQ frequency
        { key: 'useCustomLowFreq', value: false }, // Default to using preset frequencies
        { key: 'useCustomMidFreq', value: false },
        { key: 'useCustomMidLoFreq', value: false },
        { key: 'useCustomMidHiFreq', value: false },
        { key: 'useCustomHighFreq', value: false },
        // Add other default settings
      ]);
    });
  }

  // --- CRUD Methods (Examples - Add more as needed) ---

  // Tracks
  async addTrack(track: TrackInfo): Promise<string> {
    return this.tracks.put(track);
  }
  async getTrack(id: string): Promise<TrackInfo | undefined> {
    return this.tracks.get(id);
  }
  async getAllTracks(): Promise<TrackInfo[]> {
    return this.tracks.toArray();
  }
  async deleteTrack(id: string): Promise<void> {
    // Also delete related analysis and cues
    await Promise.all([
        this.tracks.delete(id),
        this.analysis.delete(id),
        this.cues.delete(id)
    ]);
  }

  // --- Analysis Related Methods ---

  /**
   * Retrieves tracks that are pending analysis.
   */
  async getTracksForAnalysis(): Promise<TrackInfo[]> {
    // Find tracks where analysisStatus is 'Pending' and they are not hidden
    return this.tracks
      .where('analysisStatus').anyOf(['Pending', 'Analyzing'])
      // .and(track => !track.isHidden) // Optional: Exclude hidden tracks
      .toArray();
  }

  /**
   * Updates the analysis status of a specific track.
   */
  async updateTrackAnalysisStatus(trackId: string, status: TrackInfo['analysisStatus']): Promise<number> {
    console.log(`DatabaseService: Updating status for track ${trackId} to ${status}`);
    return this.tracks.update(trackId, { analysisStatus: status });
  }

  /**
   * Saves the analysis results for a track and updates its status.
   * Preserves any existing external analysis data.
   */
  async updateTrackAnalysisData(trackId: string, results: Partial<TrackAnalysis>): Promise<void> {
    console.log(`DatabaseService: Saving analysis data for track ${trackId}`, results);

    // Check for existing analysis record to preserve external analysis data
    const existingAnalysis = await this.analysis.get(trackId);

    const analysisRecord: TrackAnalysis = {
        trackId: trackId,
        bpm: results.bpm,
        key: results.key,
        // Add other fields from results as needed
        // firstBeatTime: results.firstBeatTime,
        // beatGridTimes: results.beatGridTimes,
        // waveformPeaks: results.waveformPeaks,
        analysisDate: Date.now(), // Record when analysis was completed

        // Preserve external analysis data if it exists
        externalAnalysis: existingAnalysis?.externalAnalysis,
        externalAnalysisDate: existingAnalysis?.externalAnalysisDate
    };

    // Use a transaction to ensure atomicity
    await this.transaction('rw', this.analysis, this.tracks, async () => {
        await this.analysis.put(analysisRecord); // Save analysis data
        await this.tracks.update(trackId, { analysisStatus: 'Analyzed' }); // Update track status
    });
    console.log(`DatabaseService: Successfully saved analysis and updated status for track ${trackId}`);
  }


  // Analysis (Original methods - keep or remove if redundant?)
  async saveAnalysis(analysisData: TrackAnalysis): Promise<string> {
    // This might be redundant now with updateTrackAnalysisData,
    // but keep it if direct saving is needed elsewhere.
    return this.analysis.put(analysisData);
  }
  async getAnalysis(trackId: string): Promise<TrackAnalysis | undefined> {
    return this.analysis.get(trackId);
  }

  // Cues
  async saveCues(cueData: TrackCues): Promise<string> {
    return this.cues.put(cueData);
  }
  async getCues(trackId: string): Promise<TrackCues | undefined> {
    return this.cues.get(trackId);
  }

  // Settings
  async saveSetting(setting: AppSetting): Promise<string> {
    return this.settings.put(setting);
  }
  async getSetting(key: string): Promise<AppSetting | undefined> {
    return this.settings.get(key);
  }
  async getAllSettings(): Promise<AppSetting[]> {
    return this.settings.toArray();
  }

  // MIDI Mappings
  async saveMidiMapping(mapping: MidiMapping): Promise<string> {
    return this.midiMappings.put(mapping);
  }
  async loadMidiMappings(deviceName?: string): Promise<MidiMapping[]> {
    if (deviceName) {
      return this.midiMappings.where('deviceName').equals(deviceName).toArray();
    }
    return this.midiMappings.toArray();
  }
  async deleteMidiMapping(id: string): Promise<void> {
    return this.midiMappings.delete(id);
  }

  // Tracked Directories
  async saveTrackedDirectory(dir: StoredDirectoryHandle): Promise<string> {
    // Ensure isActive is true when saving/adding a new directory initially
    dir.isActive = 1;
    return this.trackedDirectories.put(dir);
  }

  async getTrackedDirectory(id: string): Promise<StoredDirectoryHandle | undefined> {
    return this.trackedDirectories.get(id);
  }

  async findTrackedDirectoryByPath(path: string): Promise<StoredDirectoryHandle | undefined> {
    // Find any directory matching the path, regardless of active status
    return this.trackedDirectories.where('path').equals(path).first();
  }

  async getAllActiveTrackedDirectories(): Promise<StoredDirectoryHandle[]> {
    // Only return directories that are currently marked as active
    return this.trackedDirectories.where('isActive').equals(1).toArray(); // Dexie uses 1 for true in indexes
  }

  async deactivateTrackedDirectory(id: string): Promise<number> {
    // Mark the directory as inactive instead of deleting
    console.log(`Deactivating directory with ID: ${id}`);
    // Also mark associated tracks as hidden? Or handle this in the scanner/store?
    // For now, just deactivate the directory itself.
    return this.trackedDirectories.update(id, { isActive: 0 });
  }

  async reactivateTrackedDirectory(id: string): Promise<number> {
    // Mark the directory as active
    console.log(`Reactivating directory with ID: ${id}`);
    return this.trackedDirectories.update(id, { isActive: 1 });
  }

  // Keep the original delete method for potential cleanup/hard delete scenarios if needed later?
  // Or remove it completely if soft delete is the only way. For now, let's comment it out.
  // async deleteTrackedDirectory(id: string): Promise<void> {
  //   // Consider if tracks from this directory should also be removed/marked
  //   return this.trackedDirectories.delete(id);
  // }
}

// Export a singleton instance of the database
export const db = new AppDatabase();

declare global {
    interface Window {
        debugDb: typeof db;
    }
}

if (typeof window !== 'undefined') {
    window.debugDb = db;
}

// Optional: Function to initialize DB connection early if needed
export async function initializeDatabase(): Promise<void> {
  try {
    await db.open();
    console.log('Database connection opened successfully.');
  } catch (error) {
    console.error('Failed to open database:', error);
  }
}

// Call initialization (e.g., in your main app entry point or a service initializer)
// initializeDatabase(); // Uncomment and place appropriately if needed
