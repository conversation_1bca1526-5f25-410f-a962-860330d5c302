import { types, flow, Instance, getRoot, cast, applySnapshot } from "mobx-state-tree";
import { db, TrackInfo, StoredDirectoryHandle } from '../services/DatabaseService';
import { toast } from "sonner";
import { RootStoreType } from "./RootStore"; // Assuming RootStore exists - uncomment if needed
import { convertToCamelot } from "../utils/musicUtils";

// --- Worker Message Interfaces (mirroring worker) ---
interface ScanProgressMessage {
    type: 'SCAN_PROGRESS';
    processed: number;
    total: number;
    currentFolder?: string;
    error?: string;
}

interface ScanCompleteMessage {
    type: 'SCAN_COMPLETE';
    added: number;
    updated: number;
    errors: number;
    totalDuration: number;
}

interface WorkerReadyMessage {
    type: 'WORKER_READY';
}

type WorkerMessage = ScanProgressMessage | ScanCompleteMessage | WorkerReadyMessage;

// --- MST Models ---

// Represents basic info about a tracked directory stored in MST state
// The actual FileSystemDirectoryHandle is kept in IndexedDB
export const TrackedDirectoryModel = types.model("TrackedDirectory", { // Added export
    id: types.identifier,
    name: types.string,
});

// Represents TrackInfo for MST state
// Keep this aligned with the TrackInfo interface in DatabaseService
export const TrackInfoModel = types.model("TrackInfo", {
    id: types.identifier, // directoryId:relativeFilePath
    filePath: types.string,
    filename: types.string,
    duration: types.maybe(types.number),
    artist: types.maybe(types.string),
    title: types.maybe(types.string),
    album: types.maybe(types.string),
    genre: types.maybe(types.string),
    rating: types.maybe(types.number),
    addedDate: types.number,
    lastPlayedDate: types.maybe(types.number),
    isHidden: types.boolean,
    analysisStatus: types.enumeration("AnalysisStatus", ["Pending", "Analyzing", "Analyzed", "Error"]),
    fileHash: types.maybe(types.string),
    duplicateOf: types.maybe(types.string),
    directoryId: types.maybe(types.string),
    // Add analysis data as a volatile property
})
.volatile(self => ({
    analysisData: null as any | null,
}))
.views(self => ({
    // Add getter for analysis data
    get hasAnalysisData() {
        return self.analysisData !== null;
    },
    // Format key based on settings store preference
    get formattedKey() {
        // Get root store to access settings
        const rootStore = getRoot(self) as any;
        if (!self.analysisData?.key) return '-';

        // Use standard notation if that's the setting or if Camelot conversion fails
        if (!rootStore.settingsStore || rootStore.settingsStore.useKeyNotation === 'standard') {
            return self.analysisData.key;
        }

        // Convert to Camelot notation when that setting is selected
        return convertToCamelot(self.analysisData.key) || self.analysisData.key;
    },
    get bpm() {
        return self.analysisData?.externalAnalysis?.bpm || self.analysisData?.bpm || 0;
    },
    
    get timeOffset() {
        // If we have external analysis with beat positions, find the first '1' beat
        if (self.analysisData?.externalAnalysis?.beat_positions && 
            self.analysisData?.externalAnalysis?.beats) {
            
            // Find index of first '1' beat position
            const firstDownbeatIndex = self.analysisData.externalAnalysis.beat_positions.findIndex(pos => pos === 1);
            
            // If found, return the corresponding beat time
            if (firstDownbeatIndex >= 0 && self.analysisData.externalAnalysis.beats[firstDownbeatIndex] !== undefined) {
                return self.analysisData.externalAnalysis.beats[firstDownbeatIndex];
            }
        }
        
        // Fallback to first beat from external analysis or internal analysis
        return self.analysisData?.externalAnalysis?.beats?.[0] || 
               self.analysisData?.firstBeatTime || 
               0;
    },
    get segments() {
        return self.analysisData?.externalAnalysis?.segments || [];
    }
}))
.actions(self => {
    const loadAnalysisData = flow(function* () {
        if (self.analysisStatus !== "Analyzed") {
            self.analysisData = null;
            return null;
        }

        try {
            // Get the root store to access databaseService
            const rootStore = getRoot(self) as any;
            if (!rootStore.databaseService) {
                console.error("Database service not available");
                return null;
            }

            const data = yield rootStore.databaseService.getAnalysis(self.id);
            self.analysisData = data;
            return data;
        } catch (error) {
            console.error(`Failed to fetch analysis data for track ${self.id}:`, error);
            self.analysisData = null;
            return null;
        }
    });

    const analysisFinished = (status: TrackInfo['analysisStatus']) => {
        self.analysisStatus = status;
        loadAnalysisData();
    };

    return {
        loadAnalysisData,
        analysisFinished,
        afterCreate() {
            // Load analysis data when the model is created
            if (self.analysisStatus === "Analyzed") {
                loadAnalysisData();
            }
        }
    };
});

const ScanProgressModel = types.model("ScanProgress", {
    processed: types.optional(types.number, 0),
    total: types.optional(types.number, 0),
    currentFolder: types.maybe(types.string),
});

const LastScanCompletionModel = types.model("LastScanCompletion", {
    added: types.number,
    updated: types.number,
    errors: types.number,
    totalDuration: types.number,
});


// --- Library Store ---

export const LibraryStoreModel = types
  .model("LibraryStore", {
    tracks: types.map(TrackInfoModel),
    trackedDirectories: types.map(TrackedDirectoryModel),
    scanStatus: types.optional(types.enumeration("ScanStatusEnum", ["idle", "scanning", "error", "complete"]), "idle"),
    scanProgress: types.optional(ScanProgressModel, {}),
    lastScanCompletion: types.maybeNull(LastScanCompletionModel),
    isScannerReady: types.optional(types.boolean, false),
    scannerError: types.maybeNull(types.string),
  })
  .volatile<{ worker: Worker | null }>(() => ({
    worker: null, // Store the worker instance in volatile state
  }))
  .actions((self) => {
    let worker: Worker | null = null; // Manage worker instance locally within actions closure

    // --- Define helper functions first ---
    const _handleWorkerMessage = flow(function* _handleWorkerMessage(data: WorkerMessage) { // Made flow to use yield
        // 'self' is automatically available here
        const rootStore = getRoot<RootStoreType>(self); // Get the root store

        switch (data.type) {
            case 'WORKER_READY':
                console.log("Scanner Worker is ready.");
                self.isScannerReady = true;
                // Need to call the action defined below, will use the final 'actions' object
                yield actions.loadTrackedDirectories(); // Use yield
                break;
            case 'SCAN_PROGRESS':
                self.scanStatus = "scanning";
                self.scanProgress.processed = data.processed;
                self.scanProgress.total = data.total;
                self.scanProgress.currentFolder = data.currentFolder;
                if (data.error) {
                    self.scannerError = self.scannerError ? `${self.scannerError}\n${data.error}` : data.error;
                    toast.warning(`Scan issue: ${data.error}`);
                }
                break;
            case 'SCAN_COMPLETE':
                self.scanStatus = data.errors > 0 ? "error" : "complete";
                self.lastScanCompletion = cast(data); // Store completion details
                self.scanProgress = cast({ processed: 0, total: 0, currentFolder: undefined }); // Reset progress fully
                toast.success(`Scan complete in ${data.totalDuration.toFixed(1)}s. Added: ${data.added}, Updated: ${data.updated}, Errors: ${data.errors}.`);
                // Need to call the action defined below, will use the final 'actions' object
                yield actions.loadTracksFromDb(); // Use yield

                // Trigger audio analysis after tracks are loaded
                if (rootStore.audioAnalysisService) {
                    console.log("LibraryStore: Scan complete, triggering audio analysis.");
                    yield rootStore.audioAnalysisService.startAnalysis(); // Trigger analysis
                } else {
                    console.error("LibraryStore: AudioAnalysisService not available to trigger analysis after scan.");
                }
                break;
            default:
                console.warn("Received unknown message from scanner worker:", data);
        }
    });

    const _loadTrackedDirectories = flow(function* loadTrackedDirectories() {
        console.log("Loading active tracked directories from DB...");
        try {
            // Use the new method to only get active directories
            const dirsFromDb: StoredDirectoryHandle[] = yield db.getAllActiveTrackedDirectories();
            const newDirsSnapshot: Record<string, Instance<typeof TrackedDirectoryModel>> = {};
            dirsFromDb.forEach(dir => {
                // Only add active directories to the MST store
                if (dir.isActive) {
                    newDirsSnapshot[dir.id] = TrackedDirectoryModel.create({ id: dir.id, name: dir.name });
                }
            });
            applySnapshot(self.trackedDirectories, newDirsSnapshot); // Efficiently update map
            console.log(`Loaded ${self.trackedDirectories.size} tracked directories.`);
        } catch (error) {
            console.error("Failed to load tracked directories:", error);
            toast.error("Failed to load tracked directories.");
        }
    });

    const _loadTracksFromDb = flow(function* loadTracksFromDb() {
        console.log("Loading tracks from DB into store...");
        try {
            // Restore original query: Load only non-hidden tracks
            const tracksFromDb: TrackInfo[] = yield db.tracks.where('isHidden').equals(0).toArray(); // Dexie uses 0 for false in indexes

            const newTracksSnapshot: Record<string, Instance<typeof TrackInfoModel>> = {};
            tracksFromDb.forEach(track => {
                // No need to check isHidden here anymore, query does it.
                // Removed debug logging for isHidden status

                const trackData = {
                    ...track,
                    duration: track.duration ?? undefined,
                    artist: track.artist ?? undefined,
                    title: track.title ?? undefined,
                    album: track.album ?? undefined,
                    genre: track.genre ?? undefined,
                    rating: track.rating ?? undefined,
                    lastPlayedDate: track.lastPlayedDate ?? undefined,
                    isHidden: Boolean(track.isHidden),
                    // Map new fields, providing undefined if they don't exist in the DB record yet
                    fileHash: track.fileHash ?? undefined,
                    duplicateOf: track.duplicateOf ?? undefined,
                    directoryId: track.directoryId ?? undefined,
                    analysisStatus: track.analysisStatus ?? "Pending"
                };
                if (!["Pending", "Analyzing", "Analyzed", "Error"].includes(trackData.analysisStatus)) {
                    console.warn(`Invalid analysisStatus "${trackData.analysisStatus}" for track ${trackData.id}, defaulting to "Pending".`);
                    trackData.analysisStatus = "Pending";
                }
                newTracksSnapshot[track.id] = TrackInfoModel.create(trackData);
            });
            applySnapshot(self.tracks, newTracksSnapshot); // Efficiently update map
            console.log(`Loaded ${self.tracks.size} tracks into store.`); // Restore original log message using self.tracks.size
        } catch (error) {
            console.error("Failed to load tracks:", error);
            toast.error("Failed to load tracks.");
        }
    });

    // --- Define the actions object, assigning the helpers ---
    // Use 'any' temporarily for actions type to break circular dependency during definition
    const actions: any = {
        _handleWorkerMessage, // Assign helper
        _loadTrackedDirectories, // Assign helper
        _loadTracksFromDb, // Assign helper

        handleWorkerError(errorMessage: string) {
            self.scanStatus = "error";
            self.scannerError = `Worker error: ${errorMessage}`;
            self.isScannerReady = false;
        },

        // --- Worker Management ---
        initializeScanner() {
            if (worker) { // Use local worker variable
                console.warn("Scanner worker already initialized.");
                return;
            }
            console.log("Initializing scanner worker...");
            try {
                // Assign to local variable
                worker = new Worker(new URL('../workers/libraryScanner.worker.ts', import.meta.url), {
                    type: 'module'
                });

                // Assign the handler to call the action (closure captures 'self' and 'actions')
                worker.onmessage = (event: MessageEvent<WorkerMessage>) => {
                    // Wrap the flow in a promise to handle potential errors and ensure it runs
                    actions._handleWorkerMessage(event.data).catch((error: any) => {
                        console.error("Error handling worker message:", error);
                        actions.handleWorkerError(`Error processing worker message: ${error.message || String(error)}`);
                    });
                };

                worker.onerror = (error: ErrorEvent) => {
                    console.error("Error in scanner worker:", error);
                    // Handle worker error through a proper action
                    actions.handleWorkerError(error.message);
                    toast.error(`Scanner Worker failed: ${error.message}`);
                };
            } catch (error) {
                 console.error("Failed to initialize scanner worker:", error);
                 self.scanStatus = "error"; // Update self properties
                 self.scannerError = `Failed to create worker: ${error instanceof Error ? error.message : String(error)}`; // Update self properties
                 self.isScannerReady = false; // Update self properties
                 toast.error(`Failed to create Scanner Worker: ${self.scannerError}`);
            }
        },

        terminateScanner() {
            if (worker) { // Use local worker variable
                console.log("Terminating scanner worker.");
                worker.terminate();
                worker = null; // Clear local variable
                self.isScannerReady = false; // Update self properties
                self.scanStatus = "idle"; // Reset status on termination
            }
        },

        // --- Scanning Actions ---
        _startScan(directoryIds?: string[]) {
            if (!worker || !self.isScannerReady) { // Use local worker variable
                toast.error("Scanner worker is not ready.");
                console.error("Attempted to scan when worker is not ready.");
                return;
            }
            if (self.scanStatus === "scanning") {
                toast.info("Scan is already in progress.");
                return;
            }

            console.log(directoryIds ? `Requesting scan for specific folders: ${directoryIds.join(', ')}` : "Requesting full library scan...");
            self.scanStatus = "scanning";
            self.scanProgress = cast({ processed: 0, total: 0, currentFolder: undefined }); // Reset progress fully
            self.lastScanCompletion = null; // Clear previous results
            self.scannerError = null; // Clear previous errors
            worker.postMessage({ type: 'SCAN_FOLDERS', directoryIds }); // Use local worker variable
        },

        scanAllFolders() {
            actions._startScan(); // Call internal action
        },

        scanSpecificFolders(directoryIds: string[]) {
            actions._startScan(directoryIds); // Call internal action
        },

        // --- Directory Management ---
        loadTrackedDirectories: _loadTrackedDirectories, // Assign helper directly

        addTrackedDirectory: flow(function* addTrackedDirectory(dirHandle: FileSystemDirectoryHandle) {
            const dirName = dirHandle.name;
            console.log(`Attempting to add directory: ${dirName}`);

            try {
                // --- Check if an INACTIVE directory with this name already exists ---
                // Note: Relying on name isn't perfectly robust, but it's a practical approach here.
                const existingInactiveDir: StoredDirectoryHandle | undefined = yield db.trackedDirectories
                    .where('name').equals(dirName)
                    .filter(dir => !dir.isActive) // Find one that is specifically inactive
                    .first();

                let dirIdToUse: string;
                let wasReactivated = false;

                if (existingInactiveDir) {
                    // --- Reactivate Existing Directory ---
                    dirIdToUse = existingInactiveDir.id;
                    wasReactivated = true;
                    console.log(`Found inactive directory "${dirName}" with ID: ${dirIdToUse}. Reactivating.`);

                    // Update handle and mark as active in DB
                    yield db.trackedDirectories.update(dirIdToUse, { handle: dirHandle, isActive: 1 });

                    // Update MST state (add if not present, or update if somehow still there)
                    const existingModel = self.trackedDirectories.get(dirIdToUse);
                    if (existingModel) {
                        // Should ideally not be in MST if inactive, but handle just in case
                        applySnapshot(existingModel, { id: dirIdToUse, name: dirName }); // Update name just in case
                    } else {
                        self.trackedDirectories.put(TrackedDirectoryModel.create({ id: dirIdToUse, name: dirName }));
                    }
                    toast.info(`Folder "${dirName}" reactivated.`);

                } else {
                    // --- Add New Directory ---
                    // Check if an ACTIVE directory with this name already exists (prevent duplicates)
                    const existingActiveDir: StoredDirectoryHandle | undefined = yield db.trackedDirectories
                        .where('name').equals(dirName)
                        .filter(dir => dir.isActive===1) // Find one that is active
                        .first();

                    if (existingActiveDir) {
                         console.warn(`Folder "${dirName}" is already actively tracked.`);
                         toast.warning(`Folder "${dirName}" is already tracked.`);
                         return; // Don't add again
                    }

                    console.log(`Directory "${dirName}" is new or was previously hard-deleted. Generating new ID.`);
                    dirIdToUse = crypto.randomUUID();

                    // Create the new entry - include path to satisfy the type, even if not used reliably
                    const newDirEntry: StoredDirectoryHandle = {
                        id: dirIdToUse,
                        name: dirName,
                        path: dirName, // Use name as placeholder path to satisfy type
                        handle: dirHandle,
                        isActive: 1, // Explicitly set isActive
                    };
                    // No cast needed now as the type matches
                    yield db.saveTrackedDirectory(newDirEntry); // Save new directory to DB

                    const newDirModel = TrackedDirectoryModel.create({ id: newDirEntry.id, name: newDirEntry.name });
                    self.trackedDirectories.put(newDirModel); // Add new directory to MST map
                    toast.success(`Folder "${dirName}" added successfully.`);
                }

                console.log(`Directory ${wasReactivated ? 'reactivated' : 'added'}: ${dirIdToUse} - ${dirName}`);

                // --- Unhide existing tracks for this directory (using the determined ID) ---
                console.log(`Checking for existing hidden tracks for directory ${dirIdToUse}...`);
                const hiddenTracksToUnhide: TrackInfo[] = yield db.tracks
                    .where('id').startsWith(dirIdToUse + ':') // Use dirIdToUse
                    .and(track => track.isHidden === 1)
                    .toArray();

                if (hiddenTracksToUnhide.length > 0) {
                    console.log(`Found ${hiddenTracksToUnhide.length} hidden tracks to unhide.`);
                    const updates = hiddenTracksToUnhide.map(track => ({
                        key: track.id,
                        changes: { isHidden: 0 as 0 | 1 }
                    }));
                    yield db.tracks.bulkUpdate(updates);
                    toast.info(`${updates.length} previously hidden track(s) from "${dirName}" are now visible.`); // Use dirName
                    console.log(`${updates.length} tracks unhidden for directory ${dirIdToUse}`); // Use dirIdToUse

                    // Update MST state for these tracks - Ensure tracks are loaded if needed
                    yield actions._loadTracksFromDb(); // Reload tracks to reflect unhiding
                    // The above might be inefficient, alternative is to manually update/add to self.tracks:
                    /*
                    updates.forEach(update => {
                        const trackInStore = self.tracks.get(update.key);
                        if (trackInStore) {
                            trackInStore.isHidden = false;
                        } else {
                           // If track wasn't in store, we need its full data to add it
                           // This part requires fetching the updated track data from DB
                           console.warn(`Track ${update.key} was not in the store. Reloading all tracks for simplicity.`);
                        }
                    });
                    */
                } else {
                    console.log("No existing hidden tracks found for this directory.");
                }
                // --- End Unhide ---


                // Trigger scan for the directory (using the determined ID)
                actions.scanSpecificFolders([dirIdToUse]); // Use dirIdToUse

            } catch (error) {
                console.error(`Error adding tracked directory ${dirName}:`, error);
                toast.error(`Failed to add folder "${dirName}".`);
            }
        }),

        removeTrackedDirectory: flow(function* removeTrackedDirectory(directoryId: string) {
            const dirToRemove = self.trackedDirectories.get(directoryId);
            if (!dirToRemove) {
                console.warn(`Attempted to remove non-existent directory ID: ${directoryId}`);
                return;
            }
            const dirName = dirToRemove.name;
            console.log(`Attempting to remove directory: ${dirName} (ID: ${directoryId})`);

            try {
                // 1. Mark associated tracks as hidden in DB
                const tracksToHide: TrackInfo[] = yield db.tracks.where('id').startsWith(directoryId + ':').toArray();
                const updates = tracksToHide.map(track => ({
                    key: track.id,
                    changes: { isHidden: 1 as 0 | 1 }
                }));
                if (updates.length > 0) {
                    yield db.tracks.bulkUpdate(updates);
                    toast.info(`${updates.length} track(s) from "${dirName}" marked as hidden.`);
                    console.log(`${updates.length} tracks marked as hidden for directory ${directoryId}`);
                    // Update MST state for these tracks
                    updates.forEach(update => {
                        const trackInStore = self.tracks.get(update.key);
                        if (trackInStore) {
                            trackInStore.isHidden = true; // Directly modify MST instance
                        }
                    });
                }

                // 2. Mark the directory as inactive in DB
                yield db.deactivateTrackedDirectory(directoryId);

                // 3. Remove from MST state (as we only show active ones)
                self.trackedDirectories.delete(directoryId);

                toast.success(`Folder "${dirName}" removed.`);
                console.log(`Directory removed: ${directoryId} - ${dirName}`);

            } catch (error) {
                console.error(`Error removing folder ${dirName} (ID: ${directoryId}):`, error);
                toast.error(`Failed to remove folder "${dirName}".`);
            }
        }),

        // --- Track Management ---
        loadTracksFromDb: _loadTracksFromDb, // Assign helper directly

        // --- Placeholder for RootStore compatibility ---
        hydrateFromDb() {
          console.log("Hydrating LibraryStore from DB...");
          // Call the internal flows via actions
          actions._loadTrackedDirectories(); // Use the final actions object
          actions._loadTracksFromDb(); // Use the final actions object
        },

        // --- Lifecycle ---
        afterCreate() {
            actions.initializeScanner(); // Call internal action
            // Initial data load is now triggered by WORKER_READY or hydrateFromDb
        },

        beforeDestroy() {
            actions.terminateScanner(); // Call internal action
        },

        requestTrackAnalysis(trackId: string) {
            const track = self.tracks.get(trackId);
            if (!track) {
                console.error(`Cannot request analysis: Track with ID ${trackId} not found.`);
                toast.error("Track not found.");
                return;
            }

            // Get the root store to access audioAnalysisService
            const rootStore = getRoot(self) as any;
            if (!rootStore.audioAnalysisService) {
                console.error("AudioAnalysisService not available to analyze track.");
                toast.error("Audio analysis service not available.");
                return;
            }

            // Update track status to pending in the store
            track.analysisStatus = "Pending";

            // Update track status in the database
            db.updateTrackAnalysisStatus(trackId, "Pending")
                .then(() => {
                    console.log(`Track ${trackId} marked as Pending for analysis.`);

                    // Add the track to the analysis queue
                    const trackInfo = {
                        id: track.id,
                        filePath: track.filePath,
                        directoryId: track.directoryId || track.id.split(':')[0], // Use directoryId if available, otherwise extract from ID
                        filename: track.filename,
                        analysisStatus: "Pending"
                    };

                    rootStore.audioAnalysisService.addTrackToQueue(trackInfo);
                    toast.info("Track added to analysis queue.");
                })
                .catch(error => {
                    console.error(`Failed to update analysis status for track ${trackId}:`, error);
                    toast.error("Failed to queue track for analysis.");
                });
        },

        checkExternalAnalysisForTrack: flow(function* checkExternalAnalysisForTrack(trackId: string) {
            const track = self.tracks.get(trackId);
            if (!track) {
                console.error(`Cannot check external analysis: Track with ID ${trackId} not found.`);
                return false;
            }

            // Get the root store to access externalAnalysisService
            const rootStore = getRoot(self) as any;
            if (!rootStore.externalAnalysisService) {
                console.error("ExternalAnalysisService not available to check external analysis.");
                return false;
            }

            try {
                // Check if external analysis exists
                const hasExternalAnalysis = yield rootStore.externalAnalysisService.checkExternalAnalysisExists(trackId);
                if (hasExternalAnalysis) {
                    console.log(`External analysis found for track ${trackId}, importing...`);
                    const success = yield rootStore.externalAnalysisService.importExternalAnalysis(trackId);
                    if (success) {
                        console.log(`Successfully imported external analysis for track ${trackId}`);
                        // Reload analysis data after import
                        yield track.loadAnalysisData();
                        return true;
                    }
                }
                return false;
            } catch (error) {
                console.error(`Error checking/importing external analysis for track ${trackId}:`, error);
                return false;
            }
        }),

        checkExternalAnalysisForAllTracks: flow(function* checkExternalAnalysisForAllTracks() {
            console.log("Checking for external analysis data for all tracks...");

            // Get the root store to access externalAnalysisService
            const rootStore = getRoot(self) as any;
            if (!rootStore.externalAnalysisService) {
                console.error("ExternalAnalysisService not available to check external analysis.");
                toast.error("External analysis service not available.");
                return;
            }

            let checkedCount = 0;
            let foundCount = 0;
            let importedCount = 0;

            // Get all tracks that don't have external analysis data
            const tracksToCheck = Array.from(self.tracks.values()).filter(track =>
                track.analysisStatus !== "Analyzing" &&
                (!track.analysisData || !track.analysisData.externalAnalysis)
            );

            if (tracksToCheck.length === 0) {
                console.log("No tracks need external analysis data.");
                toast.info("No tracks need external analysis data.");
                return;
            }

            toast.info(`Checking ${tracksToCheck.length} tracks for external analysis data...`);

            for (const track of tracksToCheck) {
                try {
                    checkedCount++;

                    // Check if external analysis exists
                    const hasExternalAnalysis = yield rootStore.externalAnalysisService.checkExternalAnalysisExists(track.id);
                    if (hasExternalAnalysis) {
                        foundCount++;
                        console.log(`External analysis found for track ${track.id}, importing...`);
                        const success = yield rootStore.externalAnalysisService.importExternalAnalysis(track.id);
                        if (success) {
                            importedCount++;
                            console.log(`Successfully imported external analysis for track ${track.id}`);
                            // Reload analysis data after import
                            yield track.loadAnalysisData();
                        }
                    }

                    // Update progress every 10 tracks
                    if (checkedCount % 10 === 0) {
                        toast.info(`Checked ${checkedCount}/${tracksToCheck.length} tracks. Found: ${foundCount}, Imported: ${importedCount}`);
                    }
                } catch (error) {
                    console.error(`Error checking/importing external analysis for track ${track.id}:`, error);
                }
            }

            toast.success(`External analysis check complete. Checked: ${checkedCount}, Found: ${foundCount}, Imported: ${importedCount}`);
        }),
    };

    return actions; // Return the actions object
  })
  .views((self) => ({
    // Get tracked directories as an array (memoized by MST)
    get trackedDirectoriesList() {
        return Array.from(self.trackedDirectories.values());
    },
    // Get non-hidden tracks as an array (memoized by MST)
    get tracksList() {
        // Explicitly cast the result of Array.from to the correct type
        const tracksArray = Array.from(self.tracks.values()) as Instance<typeof TrackInfoModel>[];
        return tracksArray.filter((track: Instance<typeof TrackInfoModel>) => !track.isHidden);
    }
  }));


// --- Types ---
export interface LibraryStoreInstance extends Instance<typeof LibraryStoreModel> {}
// export type LibraryStoreSnapshotType = SnapshotIn<typeof LibraryStoreModel>; // Use SnapshotIn if needed











