import './App.css';
import { BrowserRouter, Routes, Route, Link, Navigate } from 'react-router-dom'; // Import routing components
import FolderManager from './components/LibraryManager/FolderManager';
import LibraryPage from './components/LibraryView/LibraryPage'; // Import the new Library page
import SettingsPage from './components/SettingsPage/SettingsPage'; // Import the Settings page
import DeckPage from './components/DeckView/DeckPage'; // Import the Deck page
import AllDecksPage from './components/DeckView/AllDecksPage';
import { StoreProvider } from './contexts/StoreContext';
import { Toaster } from "sonner"; // Correct import path for Toaster
import { observer } from 'mobx-react-lite';

// Main app content with routing
const AppContent = observer(() => {

  return (
    <div className="flex flex-col h-screen w-full">
      <nav className="bg-card border-b p-4 shadow-sm">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="text-xl font-bold">Mismo.DJ</h1>
          <div className="space-x-4">
            <Link to="/library" className="text-primary hover:underline">Library</Link>
            <Link to="/decks" className="text-primary hover:underline">Decks</Link>
            <Link to="/folder_manager" className="text-primary hover:underline">Folder Manager</Link>
            <Link to="/settings" className="text-primary hover:underline">Settings</Link>
          </div>
        </div>
      </nav>

      <main className="flex-grow overflow-auto w-full">
        <Routes>
          <Route path="/library" element={<LibraryPage />} />
          <Route path="/decks" element={<AllDecksPage />} />
          <Route path="/deck/:deckId" element={<DeckPage />} />
          <Route path="/folder_manager" element={<FolderManager />} />
          <Route path="/settings" element={<SettingsPage />} />
          <Route path="/" element={<Navigate to="/decks" replace />} />
        </Routes>
      </main>

      <Toaster />
    </div>
  );
});

// Main App component with providers
function App() {
  return (
    <StoreProvider>
      <BrowserRouter>
        <AppContent />
      </BrowserRouter>
    </StoreProvider>
  );
}

export default App
