import React, { useRef, useState, useEffect, useCallback } from 'react'
import { cn } from '@/lib/utils'

export interface FaderProps {
  min?: number
  max?: number
  value?: number
  defaultValue?: number
  step?: number
  speed?: number
  acceleration?: number
  size?: "sm" | "md" | "lg"
  color?: string
  label?: string
  unit?: string
  hasStop?: boolean
  stopValue?: number
  decimals?: number
  enableDoubleClick?: boolean
  orientation?: "vertical" | "horizontal"
  onChange?: (value: number) => void
  className?: string
  disabled?: boolean
}

// Inner component that just renders the fader
const FaderRenderer = React.memo(({
  value,
  min,
  max,
  size,
  color,
  hasStop,
  stopValue,
  isAtStop,
  orientation,
  disabled,
}: {
  value: number
  min: number
  max: number
  size: "sm" | "md" | "lg"
  color: string
  hasStop: boolean
  stopValue: number
  isAtStop: boolean
  orientation: "vertical" | "horizontal"
  disabled: boolean
}) => {
  // Helper function to get the actual color value
  const getColorValue = (colorProp: string) => {
    if (colorProp.startsWith("bg-")) {
      // For Tailwind classes, extract the color name
      const colorName = colorProp.substring(3);
      return `var(--${colorName})`;
    }
    // For direct color values (like "hsl(var(--primary))" or "#ff0000")
    return colorProp;
  };

  const actualColor = getColorValue(color);

  // Convert value to position percentage with constraints to keep thumb inside track
  const valueToPosition = (val: number) => {
    const ratio = (val - min) / (max - min);
    
    // makes sure the thumb is not outside the visual fader
    return ratio * 90;
  };

  // Size classes
  const sizeClasses = {
    vertical: {
      sm: "w-8 h-32",
      md: "w-10 h-48",
      lg: "w-12 h-64"
    },
    horizontal: {
      sm: "w-32 h-8",
      md: "w-48 h-10",
      lg: "w-64 h-12"
    }
  };

  const thumbSizeClasses = {
    vertical: {
      sm: "w-8 h-4",
      md: "w-10 h-5",
      lg: "w-12 h-6"
    },
    horizontal: {
      sm: "w-4 h-8",
      md: "w-5 h-10",
      lg: "w-6 h-12"
    }
  };

  const position = valueToPosition(value);

  return (
    <div 
      className={cn(
        "relative rounded-lg bg-zinc-900 border-2 border-zinc-800 shadow-lg",
        isAtStop && "ring-1 ring-destructive",
        sizeClasses[orientation][size]
      )}
    >
      {/* Fader track with gradient */}
      <div className="absolute inset-0 rounded-lg bg-gradient-to-b from-zinc-700 to-zinc-900"></div>

      {/* Fader track filled portion */}
      <div 
        className="absolute rounded-lg bg-zinc-800"
        style={{
          ...(orientation === "vertical" 
            ? { left: 0, right: 0, bottom: 0, height: `${position}%` }
            : { top: 0, bottom: 0, left: 0, width: `${position}%` })
        }}
      />
      {/* Thumb */}
      <div 
        className={cn(
          "absolute rounded-md bg-zinc-700 border border-zinc-600 shadow-md",
          thumbSizeClasses[orientation][size]
        )}
        style={{
          ...(orientation === "vertical" 
            ? { left: "50%", bottom: `${position}%`, transform: "translateX(-50%)" }
            : { top: "50%", left: `${position}%`, transform: "translateY(-50%)" }),
          backgroundColor: actualColor
        }}
      >
        {/* Thumb details */}
        <div className="absolute inset-1 rounded-sm bg-gradient-to-b from-zinc-600 to-zinc-700"></div>
      </div>
    </div>
  );
});

FaderRenderer.displayName = "FaderRenderer";

// Main Fader component that handles drag logic
const Fader = React.forwardRef<HTMLDivElement, FaderProps>(
  ({
    min = 0,
    max = 100,
    value,
    defaultValue = 50,
    step = 1,
    speed = 1,
    acceleration = 1,
    size = "md",
    color = "hsl(var(--primary))",
    label,
    unit = "",
    hasStop = false,
    stopValue = 50,
    decimals = 2,
    enableDoubleClick = true,
    orientation = "vertical",
    onChange,
    className,
    disabled = false,
    ...props
  }, ref) => {
    const [internalValue, setInternalValue] = useState(defaultValue)
    const currentValue = value !== undefined ? value : internalValue
    const dragContainerRef = useRef<HTMLDivElement>(null)
    const isAtStop = hasStop && currentValue === stopValue
    
    // Use refs to maintain state across renders
    const stateRef = useRef({
      isDragging: false,
      startPos: 0,
      accumulator: 0,
      currentDragValue: currentValue,
      lastValue: currentValue,
      // Store props in ref to access latest values in event handlers
      // without causing effect to re-run
      props: {
        min,
        max,
        step,
        speed,
        acceleration,
        hasStop,
        disabled,
        orientation
      }
    });
    
    // Update ref when props change
    useEffect(() => {
      stateRef.current.props = {
        min,
        max,
        step,
        speed,
        acceleration,
        hasStop,
        disabled,
        orientation
      };
    }, [min, max, step, speed, acceleration, hasStop, disabled, orientation]);
    
    // Update ref when currentValue changes
    useEffect(() => {
      stateRef.current.lastValue = currentValue;
      if (!stateRef.current.isDragging) {
        stateRef.current.currentDragValue = currentValue;
      }
    }, [currentValue]);

    // Process a new value (apply step, clamp, etc.)
    const processValue = useCallback((newValue: number) => {
      const { min, max, step, hasStop } = stateRef.current.props;
      
      // Apply step
      newValue = Math.round(newValue / step) * step
      
      // Clamp to min/max
      newValue = Math.max(min, Math.min(max, newValue))
      
      // Apply stickiness when approaching the stop value
      if (hasStop && Math.abs(newValue - stopValue) < (max - min) * 0.02) {
        newValue = stopValue
      }
      
      return newValue
    }, [stopValue]);

    // Handle value changes
    const handleValueChange = useCallback((newValue: number) => {
      newValue = processValue(newValue)
      
      if (value === undefined) {
        setInternalValue(newValue)
      }
      
      if (onChange) {
        onChange(newValue)
      }
    }, [processValue, value, onChange]);

    // Setup drag handling once on mount and never re-create it
    useEffect(() => {
      if (!dragContainerRef.current) return;
      
      const container = dragContainerRef.current
      
      const onMouseDown = (e: MouseEvent) => {
        if (stateRef.current.props.disabled) return;
        
        e.preventDefault() // Prevent any default behavior
        
        const state = stateRef.current;
        state.isDragging = true;
        
        // Set value based on click position immediately
        updateValueFromMousePosition(e);
        
        container.classList.add("cursor-grabbing");
        
        // Prevent text selection during drag
        document.body.style.userSelect = "none";
      }
      
      const updateValueFromMousePosition = (e: MouseEvent) => {
        const state = stateRef.current;
        const { min, max, orientation } = state.props;
        const rect = container.getBoundingClientRect();
        
        let ratio;
        if (orientation === "vertical") {
          // For vertical, bottom is min and top is max
          const height = rect.height;
          const relativeY = Math.max(0, Math.min(height, height - (e.clientY - rect.top)));
          ratio = relativeY / height;
        } else {
          // For horizontal, left is min and right is max
          const width = rect.width;
          const relativeX = Math.max(0, Math.min(width, e.clientX - rect.left));
          ratio = relativeX / width;
        }
        
        // Calculate new value based on position
        const newValue = min + ratio * (max - min);
        handleValueChange(newValue);
      }
      
      const onMouseMove = (e: MouseEvent) => {
        const state = stateRef.current;
        if (!state.isDragging) return;
        
        updateValueFromMousePosition(e);
      }
      
      const onMouseUp = () => {
        const state = stateRef.current;
        if (!state.isDragging) return;
        
        state.isDragging = false;
        container.classList.remove("cursor-grabbing");
        
        // Restore text selection
        document.body.style.userSelect = "";
      }
      
      // Double-click handler to reset to default value
      const onDoubleClick = (e: MouseEvent) => {
        if (stateRef.current.props.disabled || !enableDoubleClick) return;
        
        e.preventDefault();
        handleValueChange(defaultValue);
      }
      
      container.addEventListener("mousedown", onMouseDown);
      document.addEventListener("mousemove", onMouseMove);
      document.addEventListener("mouseup", onMouseUp);
      
      // Add double-click event listener
      if (enableDoubleClick) {
        container.addEventListener("dblclick", onDoubleClick);
      }
      
      return () => {
        container.removeEventListener("mousedown", onMouseDown);
        document.removeEventListener("mousemove", onMouseMove);
        document.removeEventListener("mouseup", onMouseUp);
        
        // Clean up double-click listener
        if (enableDoubleClick) {
          container.removeEventListener("dblclick", onDoubleClick);
        }
      }
    }, [enableDoubleClick, defaultValue, handleValueChange]);

    const fontSizeClasses = {
      sm: "text-xs",
      md: "text-sm",
      lg: "text-base"
    }

    return (
      <div 
        ref={ref} 
        className={cn(
          "flex items-center", 
          orientation === "vertical" ? "flex-col" : "flex-row gap-4",
          className
        )}
        {...props}
      >
        {label && (
          <div className={cn("font-medium", fontSizeClasses[size], orientation === "vertical" ? "mb-2" : "")}>
            {label}
          </div>
        )}
        
        {/* Drag container that surrounds the fader */}
        <div 
          ref={dragContainerRef}
          className={cn(
            "relative",
            disabled ? "opacity-50 cursor-not-allowed" : "cursor-grab"
          )}
        >
          {/* Render the fader using the memoized component */}
          <FaderRenderer
            value={currentValue}
            min={min}
            max={max}
            size={size}
            color={color}
            hasStop={hasStop}
            stopValue={stopValue}
            isAtStop={isAtStop}
            orientation={orientation}
            disabled={disabled}
          />
        </div>
        
        {/* Value display */}
        <div className={cn("text-center", fontSizeClasses[size], orientation === "vertical" ? "mt-2" : "")}>
          {Number.isInteger(currentValue) ? currentValue : currentValue.toFixed(decimals)}{unit}
        </div>
      </div>
    )
  }
)

Fader.displayName = "Fader"

export { Fader }

